"""
笔记服务层
处理笔记相关的业务逻辑
"""
from typing import Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_, or_
from fastapi import HTTPException, status
from datetime import datetime
from ..models.note import Note, NoteStatus
from ..models.user import <PERSON>hongshuAccount
from ..api.schemas.note import (
    NoteCreate, 
    NoteUpdate,
    NoteSearchFilter,
    NoteBatchOperation
)
import logging
import re

logger = logging.getLogger(__name__)


class NoteService:
    """笔记服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_note(self, user_id: int, note_data: NoteCreate) -> Note:
        """创建笔记"""
        try:
            # 验证账号所有权
            account = self.db.query(XiaohongshuAccount).filter(
                and_(
                    XiaohongshuAccount.id == note_data.account_id,
                    XiaohongshuAccount.user_id == user_id
                )
            ).first()
            
            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="账号不存在或无权限访问"
                )
            
            # 提取笔记ID
            note_id = self._extract_note_id(note_data.note_url)
            
            # 检查笔记是否已存在
            existing_note = self.db.query(Note).filter(
                and_(
                    Note.note_url == note_data.note_url,
                    Note.account_id == note_data.account_id
                )
            ).first()
            
            if existing_note:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该笔记已存在"
                )
            
            # 创建新笔记
            db_note = Note(
                account_id=note_data.account_id,
                note_id=note_id,
                note_url=note_data.note_url,
                title=note_data.title,
                crawl_interval=note_data.crawl_interval,
                auto_reply_enabled=note_data.auto_reply_enabled,
                status=NoteStatus.ACTIVE
            )
            
            self.db.add(db_note)
            self.db.commit()
            self.db.refresh(db_note)
            
            logger.info(f"新笔记创建成功: {note_data.note_url} (用户ID: {user_id})")
            return db_note
            
        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"创建笔记时数据库错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="笔记创建失败，可能存在重复数据"
            )
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建笔记时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="笔记创建失败"
            )
    
    def get_note_by_id(self, note_id: int, user_id: int) -> Optional[Note]:
        """根据ID获取笔记"""
        return self.db.query(Note).join(XiaohongshuAccount).filter(
            and_(
                Note.id == note_id,
                XiaohongshuAccount.user_id == user_id
            )
        ).first()
    
    def get_user_notes(
        self, 
        user_id: int, 
        filters: Optional[NoteSearchFilter] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> Tuple[List[Note], int]:
        """获取用户的笔记列表"""
        query = self.db.query(Note).join(XiaohongshuAccount).filter(
            XiaohongshuAccount.user_id == user_id
        )
        
        # 应用过滤器
        if filters:
            if filters.account_id:
                query = query.filter(Note.account_id == filters.account_id)
            
            if filters.status:
                query = query.filter(Note.status == filters.status)
            
            if filters.auto_reply_enabled is not None:
                query = query.filter(Note.auto_reply_enabled == filters.auto_reply_enabled)
            
            if filters.keyword:
                keyword_filter = or_(
                    Note.title.ilike(f"%{filters.keyword}%"),
                    Note.content.ilike(f"%{filters.keyword}%")
                )
                query = query.filter(keyword_filter)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        notes = query.order_by(Note.created_at.desc()).offset(skip).limit(limit).all()
        
        return notes, total
    
    def update_note(self, note_id: int, user_id: int, note_data: NoteUpdate) -> Optional[Note]:
        """更新笔记信息"""
        try:
            note = self.get_note_by_id(note_id, user_id)
            if not note:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="笔记不存在"
                )
            
            # 应用更新
            update_data = note_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(note, field, value)
            
            self.db.commit()
            self.db.refresh(note)
            
            logger.info(f"笔记信息更新成功: {note.note_url}")
            return note
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新笔记信息时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="笔记信息更新失败"
            )
    
    def delete_note(self, note_id: int, user_id: int) -> bool:
        """删除笔记"""
        try:
            note = self.get_note_by_id(note_id, user_id)
            if not note:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="笔记不存在"
                )
            
            self.db.delete(note)
            self.db.commit()
            
            logger.info(f"笔记删除成功: {note.note_url}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除笔记时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="笔记删除失败"
            )
    
    def batch_operation(self, user_id: int, operation_data: NoteBatchOperation) -> dict:
        """批量操作笔记"""
        try:
            # 获取用户的笔记
            notes = self.db.query(Note).join(XiaohongshuAccount).filter(
                and_(
                    Note.id.in_(operation_data.note_ids),
                    XiaohongshuAccount.user_id == user_id
                )
            ).all()
            
            if len(notes) != len(operation_data.note_ids):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="部分笔记不存在或无权限访问"
                )
            
            success_count = 0
            failed_count = 0
            
            for note in notes:
                try:
                    if operation_data.operation == "activate":
                        note.status = NoteStatus.ACTIVE
                    elif operation_data.operation == "deactivate":
                        note.status = NoteStatus.PAUSED
                    elif operation_data.operation == "delete":
                        self.db.delete(note)
                    
                    success_count += 1
                except Exception as e:
                    logger.warning(f"批量操作失败 - 笔记ID: {note.id}, 错误: {e}")
                    failed_count += 1
            
            self.db.commit()
            
            result = {
                "operation": operation_data.operation,
                "total": len(operation_data.note_ids),
                "success": success_count,
                "failed": failed_count
            }
            
            logger.info(f"批量操作完成: {result}")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"批量操作时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="批量操作失败"
            )
    
    def update_note_stats(self, note_id: int, stats_data: dict) -> bool:
        """更新笔记统计信息"""
        try:
            note = self.db.query(Note).filter(Note.id == note_id).first()
            if not note:
                return False
            
            # 更新统计信息
            if 'likes_count' in stats_data:
                note.likes_count = stats_data['likes_count']
            if 'comments_count' in stats_data:
                note.comments_count = stats_data['comments_count']
            if 'shares_count' in stats_data:
                note.shares_count = stats_data['shares_count']
            if 'author_name' in stats_data:
                note.author_name = stats_data['author_name']
            if 'author_id' in stats_data:
                note.author_id = stats_data['author_id']
            if 'content' in stats_data:
                note.content = stats_data['content']
            if 'publish_time' in stats_data:
                note.publish_time = stats_data['publish_time']
            
            # 更新最后抓取时间
            note.last_crawled = datetime.utcnow()
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新笔记统计信息时发生错误: {e}")
            return False
    
    def get_notes_for_crawling(self, limit: int = 10) -> List[Note]:
        """获取需要抓取的笔记列表"""
        from datetime import datetime, timedelta
        
        # 获取状态为ACTIVE且需要抓取的笔记
        cutoff_time = datetime.utcnow()
        
        notes = self.db.query(Note).filter(
            and_(
                Note.status == NoteStatus.ACTIVE,
                or_(
                    Note.last_crawled.is_(None),
                    Note.last_crawled < cutoff_time - timedelta(seconds=Note.crawl_interval)
                )
            )
        ).order_by(Note.last_crawled.asc().nullsfirst()).limit(limit).all()
        
        return notes
    
    def _extract_note_id(self, note_url: str) -> Optional[str]:
        """从笔记URL中提取笔记ID"""
        try:
            # 小红书笔记URL的常见格式
            patterns = [
                r'/explore/([a-f0-9]+)',  # https://www.xiaohongshu.com/explore/xxxxx
                r'/discovery/item/([a-f0-9]+)',  # 旧版格式
                r'noteId=([a-f0-9]+)',  # 参数格式
            ]
            
            for pattern in patterns:
                match = re.search(pattern, note_url)
                if match:
                    return match.group(1)
            
            return None
            
        except Exception as e:
            logger.warning(f"提取笔记ID失败: {note_url}, 错误: {e}")
            return None

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
import pandas as pd

from ..models.comment import Comment
from ..models.note import Note
from ..models.reply_template import AIReply, ReplyTemplate
from ..models.xiaohongshu_account import XiaohongshuAccount


class AnalyticsService:
    """数据分析服务"""
    
    def __init__(self, db: Session, user_id: int):
        self.db = db
        self.user_id = user_id
    
    def get_comment_analytics(self, days: int = 30) -> Dict:
        """获取留言分析数据"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 基础查询
        base_query = self.db.query(Comment).filter(
            Comment.user_id == self.user_id,
            Comment.created_at >= start_date
        )
        
        # 总留言数
        total_comments = base_query.count()
        
        # 按状态分组
        status_stats = self.db.query(
            Comment.reply_status,
            func.count(Comment.id).label('count')
        ).filter(
            Comment.user_id == self.user_id,
            Comment.created_at >= start_date
        ).group_by(Comment.reply_status).all()
        
        status_data = {status: count for status, count in status_stats}
        
        # 每日留言趋势
        daily_comments = self.db.query(
            func.date(Comment.created_at).label('date'),
            func.count(Comment.id).label('count')
        ).filter(
            Comment.user_id == self.user_id,
            Comment.created_at >= start_date
        ).group_by(func.date(Comment.created_at)).all()
        
        daily_trend = [
            {
                "date": date.strftime('%Y-%m-%d'),
                "count": count
            }
            for date, count in daily_comments
        ]
        
        # 情感分析统计
        sentiment_stats = self.db.query(
            Comment.sentiment,
            func.count(Comment.id).label('count')
        ).filter(
            Comment.user_id == self.user_id,
            Comment.created_at >= start_date,
            Comment.sentiment.isnot(None)
        ).group_by(Comment.sentiment).all()
        
        sentiment_data = {sentiment: count for sentiment, count in sentiment_stats}
        
        # 回复效率统计
        replied_comments = base_query.filter(Comment.reply_status == 'replied').all()
        if replied_comments:
            reply_times = []
            for comment in replied_comments:
                if comment.reply_time and comment.created_at:
                    reply_time = (comment.reply_time - comment.created_at).total_seconds() / 3600
                    reply_times.append(reply_time)
            
            avg_reply_time = sum(reply_times) / len(reply_times) if reply_times else 0
        else:
            avg_reply_time = 0
        
        return {
            "total_comments": total_comments,
            "status_distribution": status_data,
            "daily_trend": daily_trend,
            "sentiment_distribution": sentiment_data,
            "avg_reply_time_hours": round(avg_reply_time, 2),
            "reply_rate": round((status_data.get('replied', 0) / total_comments * 100), 2) if total_comments > 0 else 0
        }
    
    def get_ai_analytics(self, days: int = 30) -> Dict:
        """获取AI使用分析数据"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # AI回复统计
        ai_replies = self.db.query(AIReply).filter(
            AIReply.user_id == self.user_id,
            AIReply.created_at >= start_date
        ).all()
        
        total_ai_replies = len(ai_replies)
        
        # 按状态分组
        status_stats = {}
        for reply in ai_replies:
            status = reply.status
            status_stats[status] = status_stats.get(status, 0) + 1
        
        # 模型使用统计
        model_stats = {}
        for reply in ai_replies:
            model = reply.model_name
            model_stats[model] = model_stats.get(model, 0) + 1
        
        # Token使用统计
        total_tokens = sum(reply.total_tokens or 0 for reply in ai_replies)
        avg_tokens = total_tokens / total_ai_replies if total_ai_replies > 0 else 0
        
        # 质量评分统计
        quality_ratings = [reply.quality_rating for reply in ai_replies if reply.quality_rating]
        avg_quality = sum(quality_ratings) / len(quality_ratings) if quality_ratings else 0
        
        # 用户反馈统计
        feedback_stats = {}
        for reply in ai_replies:
            if reply.user_feedback:
                feedback = reply.user_feedback
                feedback_stats[feedback] = feedback_stats.get(feedback, 0) + 1
        
        # 每日AI使用趋势
        daily_ai_usage = self.db.query(
            func.date(AIReply.created_at).label('date'),
            func.count(AIReply.id).label('count'),
            func.sum(AIReply.total_tokens).label('tokens')
        ).filter(
            AIReply.user_id == self.user_id,
            AIReply.created_at >= start_date
        ).group_by(func.date(AIReply.created_at)).all()
        
        daily_trend = [
            {
                "date": date.strftime('%Y-%m-%d'),
                "count": count,
                "tokens": tokens or 0
            }
            for date, count, tokens in daily_ai_usage
        ]
        
        return {
            "total_ai_replies": total_ai_replies,
            "status_distribution": status_stats,
            "model_usage": model_stats,
            "total_tokens_used": total_tokens,
            "avg_tokens_per_reply": round(avg_tokens, 2),
            "avg_quality_rating": round(avg_quality, 2),
            "user_feedback": feedback_stats,
            "daily_trend": daily_trend
        }
    
    def get_template_analytics(self) -> Dict:
        """获取模板使用分析"""
        templates = self.db.query(ReplyTemplate).filter(
            ReplyTemplate.user_id == self.user_id
        ).all()
        
        template_stats = []
        for template in templates:
            template_stats.append({
                "id": template.id,
                "name": template.name,
                "category": template.category,
                "usage_count": template.usage_count,
                "success_rate": template.success_rate,
                "is_active": template.is_active,
                "is_ai_generated": template.is_ai_generated
            })
        
        # 按分类统计
        category_stats = {}
        for template in templates:
            category = template.category
            if category not in category_stats:
                category_stats[category] = {
                    "count": 0,
                    "total_usage": 0,
                    "avg_success_rate": 0
                }
            category_stats[category]["count"] += 1
            category_stats[category]["total_usage"] += template.usage_count
        
        # 计算平均成功率
        for category in category_stats:
            templates_in_category = [t for t in templates if t.category == category]
            success_rates = [t.success_rate for t in templates_in_category if t.success_rate]
            category_stats[category]["avg_success_rate"] = (
                sum(success_rates) / len(success_rates) if success_rates else 0
            )
        
        return {
            "total_templates": len(templates),
            "active_templates": len([t for t in templates if t.is_active]),
            "ai_generated_templates": len([t for t in templates if t.is_ai_generated]),
            "template_details": template_stats,
            "category_statistics": category_stats
        }
    
    def get_account_analytics(self, days: int = 30) -> Dict:
        """获取账号表现分析"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 获取用户的所有账号
        accounts = self.db.query(XiaohongshuAccount).filter(
            XiaohongshuAccount.user_id == self.user_id
        ).all()
        
        account_stats = []
        for account in accounts:
            # 获取该账号的笔记
            notes = self.db.query(Note).filter(
                Note.account_id == account.id
            ).all()
            
            # 获取该账号笔记的留言
            note_ids = [note.id for note in notes]
            comments = self.db.query(Comment).filter(
                Comment.note_id.in_(note_ids),
                Comment.created_at >= start_date
            ).all() if note_ids else []
            
            account_stats.append({
                "account_id": account.id,
                "account_name": account.account_name,
                "is_active": account.is_active,
                "notes_count": len(notes),
                "comments_count": len(comments),
                "replied_comments": len([c for c in comments if c.reply_status == 'replied']),
                "pending_comments": len([c for c in comments if c.reply_status == 'pending'])
            })
        
        return {
            "total_accounts": len(accounts),
            "active_accounts": len([a for a in accounts if a.is_active]),
            "account_details": account_stats
        }
    
    def get_performance_metrics(self, days: int = 30) -> Dict:
        """获取性能指标"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 留言处理效率
        total_comments = self.db.query(Comment).filter(
            Comment.user_id == self.user_id,
            Comment.created_at >= start_date
        ).count()
        
        replied_comments = self.db.query(Comment).filter(
            Comment.user_id == self.user_id,
            Comment.created_at >= start_date,
            Comment.reply_status == 'replied'
        ).count()
        
        # AI使用效率
        ai_replies = self.db.query(AIReply).filter(
            AIReply.user_id == self.user_id,
            AIReply.created_at >= start_date
        ).count()
        
        ai_used_replies = self.db.query(AIReply).filter(
            AIReply.user_id == self.user_id,
            AIReply.created_at >= start_date,
            AIReply.is_used == True
        ).count()
        
        return {
            "comment_reply_rate": round((replied_comments / total_comments * 100), 2) if total_comments > 0 else 0,
            "ai_adoption_rate": round((ai_used_replies / ai_replies * 100), 2) if ai_replies > 0 else 0,
            "total_comments_processed": total_comments,
            "total_ai_replies_generated": ai_replies,
            "efficiency_score": self._calculate_efficiency_score(total_comments, replied_comments, ai_used_replies)
        }
    
    def _calculate_efficiency_score(self, total_comments: int, replied_comments: int, ai_used: int) -> float:
        """计算效率分数"""
        if total_comments == 0:
            return 0
        
        reply_rate = replied_comments / total_comments
        ai_usage_rate = ai_used / total_comments if total_comments > 0 else 0
        
        # 综合评分：回复率权重70%，AI使用率权重30%
        efficiency_score = (reply_rate * 0.7 + ai_usage_rate * 0.3) * 100
        return round(efficiency_score, 2)
    
    def generate_report(self, days: int = 30) -> Dict:
        """生成综合报告"""
        return {
            "period": f"最近{days}天",
            "generated_at": datetime.now().isoformat(),
            "comment_analytics": self.get_comment_analytics(days),
            "ai_analytics": self.get_ai_analytics(days),
            "template_analytics": self.get_template_analytics(),
            "account_analytics": self.get_account_analytics(days),
            "performance_metrics": self.get_performance_metrics(days)
        }

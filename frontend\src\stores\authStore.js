import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authService } from '../services/authService';

export const useAuthStore = create(
  persist(
    (set, get) => ({
      // 状态
      user: null,
      token: null,
      isAuthenticated: false,
      loading: false,
      error: null,

      // 登录
      login: async (credentials) => {
        set({ loading: true, error: null });
        try {
          const response = await authService.login(credentials);
          const { access_token, user } = response.data;
          
          set({
            user,
            token: access_token,
            isAuthenticated: true,
            loading: false,
            error: null,
          });
          
          // 设置API请求头
          authService.setAuthToken(access_token);
          
          return { success: true, data: response.data };
        } catch (error) {
          const errorMessage = error.response?.data?.message || '登录失败';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            loading: false,
            error: errorMessage,
          });
          return { success: false, error: errorMessage };
        }
      },

      // 注册
      register: async (userData) => {
        set({ loading: true, error: null });
        try {
          const response = await authService.register(userData);
          const { access_token, user } = response.data;
          
          set({
            user,
            token: access_token,
            isAuthenticated: true,
            loading: false,
            error: null,
          });
          
          // 设置API请求头
          authService.setAuthToken(access_token);
          
          return { success: true, data: response.data };
        } catch (error) {
          const errorMessage = error.response?.data?.message || '注册失败';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            loading: false,
            error: errorMessage,
          });
          return { success: false, error: errorMessage };
        }
      },

      // 退出登录
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          loading: false,
          error: null,
        });
        
        // 清除API请求头
        authService.clearAuthToken();
      },

      // 获取当前用户信息
      getCurrentUser: async () => {
        const { token } = get();
        if (!token) return { success: false, error: '未登录' };

        set({ loading: true });
        try {
          const response = await authService.getCurrentUser();
          const user = response.data;
          
          set({
            user,
            loading: false,
            error: null,
          });
          
          return { success: true, data: user };
        } catch (error) {
          const errorMessage = error.response?.data?.message || '获取用户信息失败';
          set({
            loading: false,
            error: errorMessage,
          });
          
          // 如果是认证错误，清除登录状态
          if (error.response?.status === 401) {
            get().logout();
          }
          
          return { success: false, error: errorMessage };
        }
      },

      // 修改密码
      changePassword: async (passwordData) => {
        set({ loading: true, error: null });
        try {
          await authService.changePassword(passwordData);
          set({ loading: false, error: null });
          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || '密码修改失败';
          set({ loading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      // 刷新令牌
      refreshToken: async () => {
        const { token } = get();
        if (!token) return { success: false, error: '未登录' };

        try {
          const response = await authService.refreshToken();
          const { access_token } = response.data;
          
          set({ token: access_token });
          authService.setAuthToken(access_token);
          
          return { success: true, data: { access_token } };
        } catch (error) {
          // 刷新失败，清除登录状态
          get().logout();
          return { success: false, error: '令牌刷新失败' };
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 初始化认证状态
      initAuth: () => {
        const { token } = get();
        if (token) {
          authService.setAuthToken(token);
          // 验证令牌有效性
          get().getCurrentUser();
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

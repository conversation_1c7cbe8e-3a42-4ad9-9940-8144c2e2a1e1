import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  Switch,
  InputNumber,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  BookOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons';

const { Title } = Typography;
const { Option } = Select;
const { Search } = Input;

const NotesPage = () => {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    loadNotes();
  }, []);

  const loadNotes = () => {
    setLoading(true);
    setTimeout(() => {
      setNotes([
        {
          id: 1,
          title: '美妆分享 - 夏日护肤心得',
          url: 'https://www.xiaohongshu.com/explore/123456',
          account_id: 1,
          account_name: '主账号',
          is_monitoring: true,
          crawl_interval: 30,
          last_crawl: '2024-01-18 10:30:00',
          comment_count: 156,
          new_comments: 12,
          created_at: '2024-01-15 09:00:00',
        },
        {
          id: 2,
          title: '时尚穿搭 - 春季搭配指南',
          url: 'https://www.xiaohongshu.com/explore/789012',
          account_id: 1,
          account_name: '主账号',
          is_monitoring: false,
          crawl_interval: 60,
          last_crawl: '2024-01-17 15:20:00',
          comment_count: 89,
          new_comments: 0,
          created_at: '2024-01-16 14:30:00',
        },
        {
          id: 3,
          title: '美食探店 - 网红咖啡厅推荐',
          url: 'https://www.xiaohongshu.com/explore/345678',
          account_id: 2,
          account_name: '备用账号',
          is_monitoring: true,
          crawl_interval: 15,
          last_crawl: '2024-01-18 11:45:00',
          comment_count: 234,
          new_comments: 8,
          created_at: '2024-01-14 16:20:00',
        },
      ]);
      setLoading(false);
    }, 1000);
  };

  // 过滤数据
  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchText.toLowerCase()) ||
                         note.url.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'monitoring' && note.is_monitoring) ||
                         (statusFilter === 'stopped' && !note.is_monitoring);
    return matchesSearch && matchesStatus;
  });

  // 表格列配置
  const columns = [
    {
      title: '笔记标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <a href={record.url} target="_blank" rel="noopener noreferrer">
              {record.url}
            </a>
          </div>
        </div>
      ),
    },
    {
      title: '所属账号',
      dataIndex: 'account_name',
      key: 'account_name',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '监控状态',
      dataIndex: 'is_monitoring',
      key: 'is_monitoring',
      render: (monitoring, record) => (
        <Space direction="vertical" size="small">
          <Tag 
            icon={monitoring ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
            color={monitoring ? 'success' : 'default'}
          >
            {monitoring ? '监控中' : '已停止'}
          </Tag>
          <div style={{ fontSize: '12px', color: '#666' }}>
            间隔: {record.crawl_interval}分钟
          </div>
        </Space>
      ),
    },
    {
      title: '留言统计',
      key: 'comments',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <div>总数: <strong>{record.comment_count}</strong></div>
          {record.new_comments > 0 && (
            <Tag color="orange">新增: {record.new_comments}</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '最后抓取',
      dataIndex: 'last_crawl',
      key: 'last_crawl',
      render: (text) => (
        <div style={{ fontSize: '12px' }}>
          {text || '从未抓取'}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看留言">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewComments(record)}
            />
          </Tooltip>
          <Tooltip title={record.is_monitoring ? '停止监控' : '开始监控'}>
            <Button
              type="link"
              icon={record.is_monitoring ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleToggleMonitoring(record)}
            />
          </Tooltip>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个笔记吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理添加笔记
  const handleAdd = () => {
    setEditingNote(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑笔记
  const handleEdit = (note) => {
    setEditingNote(note);
    form.setFieldsValue(note);
    setModalVisible(true);
  };

  // 处理删除笔记
  const handleDelete = (id) => {
    setNotes(notes.filter(note => note.id !== id));
    message.success('笔记删除成功');
  };

  // 处理切换监控状态
  const handleToggleMonitoring = (note) => {
    setNotes(notes.map(n => 
      n.id === note.id 
        ? { ...n, is_monitoring: !n.is_monitoring }
        : n
    ));
    message.success(`${note.is_monitoring ? '停止' : '开始'}监控成功`);
  };

  // 处理查看留言
  const handleViewComments = (note) => {
    // 这里可以跳转到留言管理页面，并传递笔记ID作为筛选条件
    message.info(`查看笔记"${note.title}"的留言`);
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      if (editingNote) {
        // 更新笔记
        setNotes(notes.map(note => 
          note.id === editingNote.id 
            ? { ...note, ...values }
            : note
        ));
        message.success('笔记更新成功');
      } else {
        // 添加笔记
        const newNote = {
          id: Date.now(),
          ...values,
          account_name: '主账号', // 这里应该根据选择的账号ID获取名称
          comment_count: 0,
          new_comments: 0,
          last_crawl: null,
          created_at: new Date().toLocaleString(),
        };
        setNotes([...notes, newNote]);
        message.success('笔记添加成功');
      }
      setModalVisible(false);
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // 统计数据
  const stats = {
    total: notes.length,
    monitoring: notes.filter(note => note.is_monitoring).length,
    stopped: notes.filter(note => !note.is_monitoring).length,
    totalComments: notes.reduce((sum, note) => sum + note.comment_count, 0),
  };

  return (
    <div className="notes-page">
      <div className="page-header">
        <Title level={2}>笔记管理</Title>
        <p>管理监控的小红书笔记，配置抓取参数和查看留言统计</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总笔记数"
              value={stats.total}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="监控中"
              value={stats.monitoring}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已停止"
              value={stats.stopped}
              prefix={<PauseCircleOutlined />}
              valueStyle={{ color: '#8c8c8c' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总留言数"
              value={stats.totalComments}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Search
              placeholder="搜索笔记标题或URL"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: '100%' }}
            >
              <Option value="all">全部状态</Option>
              <Option value="monitoring">监控中</Option>
              <Option value="stopped">已停止</Option>
            </Select>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadNotes}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                添加笔记
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 笔记列表 */}
      <Card title="笔记列表">
        <Table
          columns={columns}
          dataSource={filteredNotes}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 添加/编辑笔记模态框 */}
      <Modal
        title={editingNote ? '编辑笔记' : '添加笔记'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="title"
            label="笔记标题"
            rules={[
              { required: true, message: '请输入笔记标题' },
              { max: 100, message: '标题不能超过100个字符' },
            ]}
          >
            <Input placeholder="请输入笔记标题" />
          </Form.Item>

          <Form.Item
            name="url"
            label="笔记URL"
            rules={[
              { required: true, message: '请输入笔记URL' },
              { type: 'url', message: '请输入有效的URL' },
            ]}
          >
            <Input placeholder="https://www.xiaohongshu.com/explore/..." />
          </Form.Item>

          <Form.Item
            name="account_id"
            label="所属账号"
            rules={[{ required: true, message: '请选择所属账号' }]}
          >
            <Select placeholder="请选择账号">
              <Option value={1}>主账号</Option>
              <Option value={2}>备用账号</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="crawl_interval"
                label="抓取间隔(分钟)"
                rules={[{ required: true, message: '请设置抓取间隔' }]}
                initialValue={30}
              >
                <InputNumber
                  min={5}
                  max={1440}
                  style={{ width: '100%' }}
                  placeholder="5-1440分钟"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_monitoring"
                label="启用监控"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingNote ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default NotesPage;

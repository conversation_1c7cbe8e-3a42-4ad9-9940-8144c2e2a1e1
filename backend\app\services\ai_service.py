import asyncio
import json
import re
from typing import Dict, <PERSON>, Optional, Tu<PERSON>
from datetime import datetime, timedelta

import openai
from sqlalchemy.orm import Session
from loguru import logger

from ..models.reply_template import AIReply, AIConfig, ReplyTemplate, AIRule
from ..models.comment import Comment
from ..core.config import settings
from .cache_service import ai_reply_cache


class AIService:
    """AI服务类，处理智能回复生成"""
    
    def __init__(self, db: Session, user_id: int):
        self.db = db
        self.user_id = user_id
        self.config = self._get_ai_config()
        self._setup_openai()
    
    def _get_ai_config(self) -> AIConfig:
        """获取AI配置"""
        config = self.db.query(AIConfig).filter(
            AIConfig.user_id == self.user_id
        ).first()
        
        if not config:
            # 创建默认配置
            config = AIConfig(
                user_id=self.user_id,
                api_key=settings.OPENAI_API_KEY,
                default_model="gpt-3.5-turbo",
                temperature="0.7",
                max_tokens=150,
                reply_language="zh",
                reply_tone="friendly"
            )
            self.db.add(config)
            self.db.commit()
            self.db.refresh(config)
        
        return config
    
    def _setup_openai(self):
        """设置OpenAI客户端"""
        if self.config.api_key:
            openai.api_key = self.config.api_key
            if self.config.api_base_url:
                openai.api_base = self.config.api_base_url
    
    async def generate_reply(
        self,
        comment_content: str,
        context: Optional[Dict] = None,
        template_id: Optional[int] = None
    ) -> Dict:
        """生成AI回复"""
        try:
            # 检查缓存
            cached_reply = ai_reply_cache.get_reply(
                comment_content,
                self.user_id,
                context,
                template_id
            )
            if cached_reply:
                logger.info("使用缓存的AI回复")
                return cached_reply

            # 检查每日限制
            if not self._check_daily_limit():
                return {
                    "success": False,
                    "error": "已达到每日请求限制",
                    "reply": None
                }
            
            # 构建提示词
            prompt = self._build_prompt(comment_content, context, template_id)
            
            # 调用OpenAI API
            response = await self._call_openai_api(prompt)
            
            if response["success"]:
                # 保存AI回复记录
                ai_reply = await self._save_ai_reply(
                    comment_content=comment_content,
                    generated_reply=response["reply"],
                    template_id=template_id,
                    api_response=response["raw_response"]
                )
                
                # 更新使用统计
                self._update_usage_stats()

                result = {
                    "success": True,
                    "reply": response["reply"],
                    "ai_reply_id": ai_reply.id,
                    "confidence_score": response.get("confidence_score"),
                    "tokens_used": response.get("tokens_used")
                }

                # 缓存结果
                ai_reply_cache.set_reply(
                    comment_content,
                    result,
                    self.user_id,
                    context,
                    template_id
                )

                return result
            else:
                return response
                
        except Exception as e:
            logger.error(f"AI回复生成失败: {str(e)}")
            return {
                "success": False,
                "error": f"生成失败: {str(e)}",
                "reply": None
            }
    
    def _build_prompt(
        self, 
        comment_content: str, 
        context: Optional[Dict] = None,
        template_id: Optional[int] = None
    ) -> str:
        """构建AI提示词"""
        
        # 基础提示词
        base_prompt = f"""
你是一个专业的小红书客服助手，需要为用户留言生成合适的回复。

回复要求：
1. 语言：{self.config.reply_language}
2. 语调：{self.config.reply_tone}
3. 长度：简洁明了，不超过100字
4. 风格：友好、专业、有帮助
"""
        
        if self.config.include_emoji:
            base_prompt += "5. 可以适当使用表情符号\n"
        
        # 添加模板信息
        if template_id:
            template = self.db.query(ReplyTemplate).filter(
                ReplyTemplate.id == template_id,
                ReplyTemplate.user_id == self.user_id
            ).first()
            
            if template:
                base_prompt += f"\n参考模板：{template.content}\n"
        
        # 添加上下文信息
        if context:
            if context.get("note_title"):
                base_prompt += f"\n笔记标题：{context['note_title']}\n"
            if context.get("note_category"):
                base_prompt += f"笔记分类：{context['note_category']}\n"
        
        # 用户留言
        prompt = f"{base_prompt}\n用户留言：{comment_content}\n\n请生成一个合适的回复："
        
        return prompt
    
    async def _call_openai_api(self, prompt: str) -> Dict:
        """调用OpenAI API"""
        try:
            response = await openai.ChatCompletion.acreate(
                model=self.config.default_model,
                messages=[
                    {"role": "system", "content": "你是一个专业的小红书客服助手。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=float(self.config.temperature),
                max_tokens=self.config.max_tokens,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0
            )
            
            reply_content = response.choices[0].message.content.strip()
            
            # 内容过滤
            if self.config.content_filter:
                reply_content = self._filter_content(reply_content)
            
            return {
                "success": True,
                "reply": reply_content,
                "raw_response": response,
                "tokens_used": response.usage.total_tokens,
                "confidence_score": self._calculate_confidence_score(response)
            }
            
        except openai.error.RateLimitError:
            return {
                "success": False,
                "error": "API请求频率限制，请稍后重试",
                "reply": None
            }
        except openai.error.InvalidRequestError as e:
            return {
                "success": False,
                "error": f"请求参数错误: {str(e)}",
                "reply": None
            }
        except openai.error.AuthenticationError:
            return {
                "success": False,
                "error": "API密钥无效，请检查配置",
                "reply": None
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"API调用失败: {str(e)}",
                "reply": None
            }
    
    async def _save_ai_reply(
        self,
        comment_content: str,
        generated_reply: str,
        template_id: Optional[int],
        api_response: Dict
    ) -> AIReply:
        """保存AI回复记录"""
        
        usage = api_response.get("usage", {})
        
        ai_reply = AIReply(
            comment_id=None,  # 如果有comment_id可以传入
            template_id=template_id,
            original_content=comment_content,
            generated_reply=generated_reply,
            model_name=self.config.default_model,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens,
            prompt_tokens=usage.get("prompt_tokens"),
            completion_tokens=usage.get("completion_tokens"),
            total_tokens=usage.get("total_tokens"),
            confidence_score=self._calculate_confidence_score(api_response),
            user_id=self.user_id
        )
        
        self.db.add(ai_reply)
        self.db.commit()
        self.db.refresh(ai_reply)
        
        return ai_reply
    
    def _filter_content(self, content: str) -> str:
        """内容过滤"""
        # 移除敏感词汇
        sensitive_words = ["广告", "推广", "加微信", "联系方式"]
        for word in sensitive_words:
            content = content.replace(word, "***")
        
        # 移除过长的内容
        if len(content) > 200:
            content = content[:200] + "..."
        
        return content
    
    def _calculate_confidence_score(self, response: Dict) -> int:
        """计算置信度分数"""
        # 基于token使用量和模型响应质量计算置信度
        # 这里是简化的计算逻辑
        if hasattr(response, 'choices') and response.choices:
            finish_reason = response.choices[0].finish_reason
            if finish_reason == "stop":
                return 85  # 正常完成
            elif finish_reason == "length":
                return 70  # 长度限制
            else:
                return 60  # 其他情况
        return 50
    
    def _check_daily_limit(self) -> bool:
        """检查每日请求限制"""
        today = datetime.now().date()
        
        # 重置每日计数器
        if self.config.updated_at and self.config.updated_at.date() < today:
            self.config.current_daily_requests = 0
        
        return self.config.current_daily_requests < self.config.max_daily_requests
    
    def _update_usage_stats(self):
        """更新使用统计"""
        self.config.current_daily_requests += 1
        self.db.commit()
    
    async def get_reply_suggestions(
        self, 
        comment_content: str, 
        limit: int = 3
    ) -> List[Dict]:
        """获取回复建议"""
        suggestions = []
        
        # 基于规则匹配模板
        matched_templates = self._match_templates_by_rules(comment_content)
        
        for template in matched_templates[:limit]:
            # 使用模板生成回复
            reply_result = await self.generate_reply(
                comment_content=comment_content,
                template_id=template.id
            )
            
            if reply_result["success"]:
                suggestions.append({
                    "template_id": template.id,
                    "template_name": template.name,
                    "reply": reply_result["reply"],
                    "confidence": reply_result.get("confidence_score", 0)
                })
        
        return suggestions
    
    def _match_templates_by_rules(self, comment_content: str) -> List[ReplyTemplate]:
        """基于规则匹配模板"""
        # 获取活跃的规则
        rules = self.db.query(AIRule).filter(
            AIRule.user_id == self.user_id,
            AIRule.is_active == True
        ).order_by(AIRule.priority.desc()).all()
        
        matched_templates = []
        
        for rule in rules:
            if self._check_rule_match(rule, comment_content):
                # 获取规则关联的模板
                if rule.template_ids:
                    template_ids = rule.template_ids
                    templates = self.db.query(ReplyTemplate).filter(
                        ReplyTemplate.id.in_(template_ids),
                        ReplyTemplate.user_id == self.user_id,
                        ReplyTemplate.is_active == True
                    ).all()
                    matched_templates.extend(templates)
        
        # 去重并按使用次数排序
        unique_templates = list({t.id: t for t in matched_templates}.values())
        return sorted(unique_templates, key=lambda x: x.usage_count, reverse=True)
    
    def _check_rule_match(self, rule: AIRule, comment_content: str) -> bool:
        """检查规则是否匹配"""
        # 关键词匹配
        if rule.keywords:
            keywords = rule.keywords
            for keyword in keywords:
                if keyword.lower() in comment_content.lower():
                    return True
        
        # 这里可以添加更多匹配逻辑，如情感分析等
        
        return False
    
    async def batch_generate_replies(
        self, 
        comments: List[Dict]
    ) -> List[Dict]:
        """批量生成回复"""
        results = []
        
        for comment in comments:
            result = await self.generate_reply(
                comment_content=comment["content"],
                context=comment.get("context")
            )
            results.append({
                "comment_id": comment.get("id"),
                "result": result
            })
            
            # 添加延迟避免API限制
            await asyncio.sleep(0.5)
        
        return results

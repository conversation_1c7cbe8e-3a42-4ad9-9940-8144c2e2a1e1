# 系统架构文档

## 概述

小红书自动回复工具采用现代化的微服务架构，前后端分离设计，具有高可扩展性、高可维护性和高性能的特点。

## 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (FastAPI)  │    │ 数据库(PostgreSQL)│
│                 │    │                 │    │                 │
│ - 用户界面      │◄──►│ - API服务       │◄──►│ - 用户数据      │
│ - 状态管理      │    │ - 业务逻辑      │    │ - 笔记数据      │
│ - 路由管理      │    │ - 数据验证      │    │ - 留言数据      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  爬虫服务        │
                       │                 │
                       │ - 数据抓取      │
                       │ - 任务调度      │
                       │ - 状态监控      │
                       └─────────────────┘
```

## 技术栈

### 后端技术栈

#### 核心框架
- **FastAPI**: 现代化的Python Web框架
  - 自动API文档生成
  - 高性能异步处理
  - 类型提示支持
  - 依赖注入系统

#### 数据层
- **SQLAlchemy**: ORM框架
  - 数据库抽象层
  - 关系映射
  - 查询构建器
- **PostgreSQL**: 关系型数据库
  - ACID事务支持
  - 复杂查询优化
  - 数据一致性保证
- **Alembic**: 数据库迁移工具
  - 版本控制
  - 自动迁移
  - 回滚支持

#### 认证与安全
- **JWT**: 无状态认证
- **BCrypt**: 密码哈希
- **CORS**: 跨域资源共享
- **OAuth2**: 标准认证流程

#### 爬虫技术
- **Playwright**: 浏览器自动化
  - 现代浏览器支持
  - 反检测能力
  - 异步操作
- **APScheduler**: 任务调度
  - 定时任务
  - 任务持久化
  - 集群支持

### 前端技术栈

#### 核心框架
- **React 18**: 用户界面库
  - 组件化开发
  - 虚拟DOM
  - Hooks API
- **Vite**: 构建工具
  - 快速热重载
  - ES模块支持
  - 优化构建

#### UI组件
- **Ant Design**: 企业级UI库
  - 丰富的组件
  - 一致的设计语言
  - 国际化支持

#### 状态管理
- **Zustand**: 轻量级状态管理
  - 简单API
  - TypeScript支持
  - 中间件扩展

## 系统分层架构

### 后端分层

```
┌─────────────────────────────────────────┐
│              API层 (Controllers)         │
│ - 路由定义                              │
│ - 请求验证                              │
│ - 响应格式化                            │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│             业务逻辑层 (Services)         │
│ - 业务规则                              │
│ - 数据处理                              │
│ - 外部服务集成                          │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│             数据访问层 (Models)          │
│ - ORM模型                              │
│ - 数据库操作                            │
│ - 数据验证                              │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               数据库层                   │
│ - PostgreSQL                           │
│ - 数据持久化                            │
│ - 事务管理                              │
└─────────────────────────────────────────┘
```

### 前端分层

```
┌─────────────────────────────────────────┐
│              视图层 (Pages)              │
│ - 页面组件                              │
│ - 路由配置                              │
│ - 用户交互                              │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│             组件层 (Components)          │
│ - 可复用组件                            │
│ - UI逻辑                               │
│ - 状态管理                              │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│             服务层 (Services)            │
│ - API调用                              │
│ - 数据转换                              │
│ - 错误处理                              │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│             工具层 (Utils)               │
│ - 通用函数                              │
│ - 常量定义                              │
│ - 类型定义                              │
└─────────────────────────────────────────┘
```

## 数据库设计

### 核心实体关系

```
┌─────────────┐    ┌─────────────────┐    ┌─────────────┐
│    User     │    │ XiaohongshuAccount│    │    Note     │
│             │    │                 │    │             │
│ - id        │◄──►│ - id            │◄──►│ - id        │
│ - username  │    │ - user_id       │    │ - account_id│
│ - email     │    │ - account_name  │    │ - note_url  │
│ - password  │    │ - cookies       │    │ - title     │
└─────────────┘    └─────────────────┘    └─────────────┘
                                                  │
                                                  ▼
                                          ┌─────────────┐
                                          │   Comment   │
                                          │             │
                                          │ - id        │
                                          │ - note_id   │
                                          │ - content   │
                                          │ - status    │
                                          └─────────────┘
```

### 数据模型详细设计

#### 用户模型 (User)
- **主键**: id (自增)
- **唯一约束**: username, email
- **索引**: username, email, created_at
- **关系**: 一对多 XiaohongshuAccount

#### 小红书账号模型 (XiaohongshuAccount)
- **主键**: id (自增)
- **外键**: user_id → User.id
- **索引**: user_id, account_id, is_active
- **关系**: 
  - 多对一 User
  - 一对多 Note

#### 笔记模型 (Note)
- **主键**: id (自增)
- **外键**: account_id → XiaohongshuAccount.id
- **唯一约束**: (account_id, note_url)
- **索引**: account_id, status, last_crawled
- **关系**:
  - 多对一 XiaohongshuAccount
  - 一对多 Comment

#### 留言模型 (Comment)
- **主键**: id (自增)
- **外键**: note_id → Note.id
- **唯一约束**: (note_id, comment_id)
- **索引**: note_id, status, publish_time
- **关系**: 多对一 Note

## 安全架构

### 认证流程

```
1. 用户登录 → 2. 验证凭据 → 3. 生成JWT → 4. 返回令牌
                    │                           │
                    ▼                           ▼
              数据库验证                    客户端存储
                    │                           │
                    ▼                           ▼
5. 后续请求携带令牌 ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ←
                    │
                    ▼
6. 验证令牌 → 7. 提取用户信息 → 8. 授权访问
```

### 安全措施

#### 认证安全
- JWT令牌认证
- 密码BCrypt加密
- 令牌过期机制
- 刷新令牌支持

#### 数据安全
- SQL注入防护
- XSS攻击防护
- CSRF保护
- 输入验证

#### 访问控制
- 基于角色的访问控制
- 资源级权限验证
- API频率限制
- 跨域请求控制

## 性能优化

### 数据库优化
- 索引优化
- 查询优化
- 连接池管理
- 读写分离

### 缓存策略
- Redis缓存
- 查询结果缓存
- 会话缓存
- 静态资源缓存

### 异步处理
- 异步API
- 后台任务
- 消息队列
- 事件驱动

## 监控与日志

### 日志系统
- 结构化日志
- 日志级别管理
- 日志轮转
- 集中化日志

### 监控指标
- API响应时间
- 数据库性能
- 系统资源使用
- 错误率统计

### 告警机制
- 实时告警
- 阈值监控
- 邮件通知
- 短信通知

## 部署架构

### 容器化部署

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx         │    │   FastAPI       │    │  PostgreSQL     │
│   (反向代理)     │◄──►│   (API服务)     │◄──►│   (数据库)      │
│   - 负载均衡    │    │   - 业务逻辑    │    │   - 数据存储    │
│   - SSL终止     │    │   - 认证授权    │    │   - 事务处理    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Redis         │
                       │   (缓存)        │
                       │   - 会话存储    │
                       │   - 查询缓存    │
                       └─────────────────┘
```

### 扩展性设计

#### 水平扩展
- 无状态API设计
- 负载均衡
- 数据库分片
- 微服务拆分

#### 垂直扩展
- 资源优化
- 性能调优
- 硬件升级
- 缓存增强

## 开发规范

### 代码规范
- PEP 8 (Python)
- ESLint (JavaScript)
- 类型提示
- 文档注释

### 测试策略
- 单元测试
- 集成测试
- API测试
- 端到端测试

### 版本控制
- Git工作流
- 分支策略
- 代码审查
- 持续集成

## 技术债务管理

### 代码质量
- 代码复杂度控制
- 重复代码消除
- 技术债务跟踪
- 重构计划

### 依赖管理
- 依赖版本控制
- 安全漏洞扫描
- 许可证合规
- 定期更新

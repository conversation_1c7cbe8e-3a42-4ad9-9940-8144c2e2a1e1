version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: xiaohongshu_db
    environment:
      POSTGRES_DB: xiaohongshu_auto_reply
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - xiaohongshu_network

  # 后端API服务
  backend:
    build: ./backend
    container_name: xiaohongshu_backend
    environment:
      - DATABASE_URL=***********************************************/xiaohongshu_auto_reply
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    volumes:
      - ./backend:/app
    networks:
      - xiaohongshu_network

  # 前端服务
  frontend:
    build: ./frontend
    container_name: xiaohongshu_frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - xiaohongshu_network

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: xiaohongshu_redis
    ports:
      - "6379:6379"
    networks:
      - xiaohongshu_network

volumes:
  postgres_data:

networks:
  xiaohongshu_network:
    driver: bridge

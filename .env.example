# 数据库配置
DATABASE_URL=postgresql://postgres:password123@localhost:5432/xia<PERSON><PERSON><PERSON>_auto_reply

# JWT密钥
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# 小红书配置
XIAOHONGSHU_LOGIN_URL=https://www.xiaohongshu.com/explore
XIAOHONGSHU_BASE_URL=https://www.xiaohongshu.com

# 爬虫配置
CRAWLER_DELAY_MIN=2
CRAWLER_DELAY_MAX=5
CRAWLER_TIMEOUT=30

# Redis配置（可选）
REDIS_URL=redis://localhost:6379

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 开发环境配置
DEBUG=True
ENVIRONMENT=development

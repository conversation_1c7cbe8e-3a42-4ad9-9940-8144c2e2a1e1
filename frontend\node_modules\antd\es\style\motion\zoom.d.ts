import type { CSSInterpolation } from '@ant-design/cssinjs';
import { Keyframes } from '@ant-design/cssinjs';
import type { AliasToken, TokenWithCommonCls } from '../../theme/internal';
export declare const zoomIn: Keyframes;
export declare const zoomOut: Keyframes;
export declare const zoomBigIn: Keyframes;
export declare const zoomBigOut: Keyframes;
export declare const zoomUpIn: Keyframes;
export declare const zoomUpOut: Keyframes;
export declare const zoomLeftIn: Keyframes;
export declare const zoomLeftOut: Keyframes;
export declare const zoomRightIn: Keyframes;
export declare const zoomRightOut: Keyframes;
export declare const zoomDownIn: Keyframes;
export declare const zoomDownOut: Keyframes;
type ZoomMotionTypes = 'zoom' | 'zoom-big' | 'zoom-big-fast' | 'zoom-left' | 'zoom-right' | 'zoom-up' | 'zoom-down';
export declare const initZoomMotion: (token: TokenWithCommonCls<AliasToken>, motionName: ZoomMotionTypes) => CSSInterpolation;
export {};

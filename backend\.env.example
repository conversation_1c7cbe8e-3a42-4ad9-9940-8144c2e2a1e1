# 应用配置
APP_NAME=小红书自动回复工具
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/x<PERSON><PERSON><PERSON><PERSON>_auto_reply
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=xia<PERSON><PERSON><PERSON>_auto_reply

# 安全配置
SECRET_KEY=your_very_secure_secret_key_here_at_least_32_characters
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000","https://yourdomain.com"]

# API配置
API_V1_STR=/api/v1

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/xiaohongshu/app.log

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379/0

# 邮件配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_TLS=true
SMTP_SSL=false

# 第三方服务配置 (可选)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 爬虫配置
CRAWLER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
CRAWLER_DELAY_MIN=1
CRAWLER_DELAY_MAX=3
CRAWLER_TIMEOUT=30
CRAWLER_RETRY_TIMES=3

# 监控配置 (可选)
SENTRY_DSN=your_sentry_dsn_here
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# 文件存储配置 (可选)
UPLOAD_DIR=/var/uploads/xiaohongshu
MAX_UPLOAD_SIZE=10485760  # 10MB

# 缓存配置
CACHE_TTL=3600  # 1小时
CACHE_MAX_SIZE=1000

# 任务队列配置 (可选)
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# 开发工具配置
ENABLE_PROFILER=false
ENABLE_DEBUG_TOOLBAR=false

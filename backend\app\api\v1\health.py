"""
健康检查API
"""
import time
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text

from ...core.deps import get_db
from ...services.cache_service import cache_service
from ...api.responses import success_response

router = APIRouter()


@router.get("/health")
def health_check():
    """基础健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "xiaohongshu-auto-reply"
    }


@router.get("/health/detailed")
def detailed_health_check(db: Session = Depends(get_db)):
    """详细健康检查"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "xiaohongshu-auto-reply",
        "version": "1.0.0",
        "checks": {}
    }
    
    # 数据库健康检查
    try:
        start_time = time.time()
        db.execute(text("SELECT 1"))
        db_response_time = (time.time() - start_time) * 1000
        
        health_status["checks"]["database"] = {
            "status": "healthy",
            "response_time_ms": round(db_response_time, 2)
        }
    except Exception as e:
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    # 缓存健康检查
    try:
        start_time = time.time()
        test_key = "health_check_test"
        cache_service.set(test_key, "test_value", ttl=60)
        cached_value = cache_service.get(test_key)
        cache_service.delete(test_key)
        
        cache_response_time = (time.time() - start_time) * 1000
        
        if cached_value == "test_value":
            health_status["checks"]["cache"] = {
                "status": "healthy",
                "response_time_ms": round(cache_response_time, 2)
            }
        else:
            health_status["checks"]["cache"] = {
                "status": "unhealthy",
                "error": "Cache value mismatch"
            }
            health_status["status"] = "unhealthy"
            
    except Exception as e:
        health_status["checks"]["cache"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    # 外部服务健康检查（OpenAI API）
    try:
        # 这里可以添加对OpenAI API的简单检查
        health_status["checks"]["openai"] = {
            "status": "healthy",
            "note": "API key configured"
        }
    except Exception as e:
        health_status["checks"]["openai"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    return health_status


@router.get("/metrics")
def get_metrics(db: Session = Depends(get_db)):
    """获取系统指标"""
    try:
        # 数据库统计
        from ...models.user import User
        from ...models.comment import Comment
        from ...models.reply_template import AIReply
        
        user_count = db.query(User).count()
        comment_count = db.query(Comment).count()
        ai_reply_count = db.query(AIReply).count()
        
        # 缓存统计
        cache_stats = cache_service.get_stats()
        
        metrics = {
            "database": {
                "total_users": user_count,
                "total_comments": comment_count,
                "total_ai_replies": ai_reply_count
            },
            "cache": cache_stats,
            "system": {
                "timestamp": datetime.now().isoformat(),
                "uptime": "unknown"  # 可以添加实际的运行时间计算
            }
        }
        
        return success_response(data=metrics, message="获取系统指标成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


@router.get("/status")
def get_system_status():
    """获取系统状态"""
    import psutil
    import os
    
    try:
        # 系统资源使用情况
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 进程信息
        process = psutil.Process(os.getpid())
        process_memory = process.memory_info()
        
        status = {
            "system": {
                "cpu_usage_percent": cpu_percent,
                "memory": {
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_percent": memory.percent
                },
                "disk": {
                    "total_gb": round(disk.total / (1024**3), 2),
                    "free_gb": round(disk.free / (1024**3), 2),
                    "used_percent": round((disk.used / disk.total) * 100, 2)
                }
            },
            "process": {
                "pid": os.getpid(),
                "memory_mb": round(process_memory.rss / (1024**2), 2),
                "cpu_percent": process.cpu_percent(),
                "num_threads": process.num_threads()
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return success_response(data=status, message="获取系统状态成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


@router.get("/readiness")
def readiness_check(db: Session = Depends(get_db)):
    """就绪检查 - 用于Kubernetes等容器编排"""
    try:
        # 检查数据库连接
        db.execute(text("SELECT 1"))
        
        # 检查缓存连接
        cache_service.set("readiness_test", "ok", ttl=10)
        
        return {"status": "ready", "timestamp": datetime.now().isoformat()}
        
    except Exception as e:
        raise HTTPException(
            status_code=503, 
            detail=f"Service not ready: {str(e)}"
        )


@router.get("/liveness")
def liveness_check():
    """存活检查 - 用于Kubernetes等容器编排"""
    return {
        "status": "alive",
        "timestamp": datetime.now().isoformat()
    }

"""
留言服务层
处理留言相关的业务逻辑
"""
from typing import Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_, or_
from fastapi import HTTPException, status
from datetime import datetime
from ..models.comment import Comment, CommentStatus
from ..models.note import Note
from ..models.user import XiaohongshuAccount
from ..api.schemas.comment import (
    CommentCreate, 
    CommentUpdate,
    CommentSearchFilter,
    CommentReply,
    CommentBatchReply
)
import logging

logger = logging.getLogger(__name__)


class CommentService:
    """留言服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_comment(self, comment_data: CommentCreate) -> Comment:
        """创建留言"""
        try:
            # 检查留言是否已存在
            existing_comment = self.db.query(Comment).filter(
                and_(
                    Comment.comment_id == comment_data.comment_id,
                    Comment.note_id == comment_data.note_id
                )
            ).first()
            
            if existing_comment:
                return existing_comment  # 返回已存在的留言
            
            # 创建新留言
            db_comment = Comment(
                note_id=comment_data.note_id,
                comment_id=comment_data.comment_id,
                content=comment_data.content,
                author_name=comment_data.author_name,
                author_id=comment_data.author_id,
                parent_comment_id=comment_data.parent_comment_id,
                publish_time=comment_data.publish_time or datetime.utcnow(),
                status=CommentStatus.NEW
            )
            
            self.db.add(db_comment)
            self.db.commit()
            self.db.refresh(db_comment)
            
            logger.info(f"新留言创建成功: {comment_data.comment_id}")
            return db_comment
            
        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"创建留言时数据库错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="留言创建失败"
            )
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建留言时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="留言创建失败"
            )
    
    def get_comment_by_id(self, comment_id: int, user_id: int) -> Optional[Comment]:
        """根据ID获取留言"""
        return self.db.query(Comment).join(Note).join(XiaohongshuAccount).filter(
            and_(
                Comment.id == comment_id,
                XiaohongshuAccount.user_id == user_id
            )
        ).first()
    
    def get_user_comments(
        self, 
        user_id: int, 
        filters: Optional[CommentSearchFilter] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> Tuple[List[Comment], int]:
        """获取用户的留言列表"""
        query = self.db.query(Comment).join(Note).join(XiaohongshuAccount).filter(
            XiaohongshuAccount.user_id == user_id
        )
        
        # 应用过滤器
        if filters:
            if filters.note_id:
                query = query.filter(Comment.note_id == filters.note_id)
            
            if filters.status:
                query = query.filter(Comment.status == filters.status)
            
            if filters.author_name:
                query = query.filter(Comment.author_name.ilike(f"%{filters.author_name}%"))
            
            if filters.keyword:
                query = query.filter(Comment.content.ilike(f"%{filters.keyword}%"))
            
            if filters.date_from:
                query = query.filter(Comment.publish_time >= filters.date_from)
            
            if filters.date_to:
                query = query.filter(Comment.publish_time <= filters.date_to)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        comments = query.order_by(Comment.publish_time.desc()).offset(skip).limit(limit).all()
        
        return comments, total
    
    def update_comment(self, comment_id: int, user_id: int, comment_data: CommentUpdate) -> Optional[Comment]:
        """更新留言信息"""
        try:
            comment = self.get_comment_by_id(comment_id, user_id)
            if not comment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="留言不存在"
                )
            
            # 应用更新
            update_data = comment_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(comment, field, value)
            
            self.db.commit()
            self.db.refresh(comment)
            
            logger.info(f"留言信息更新成功: {comment.comment_id}")
            return comment
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新留言信息时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="留言信息更新失败"
            )
    
    def reply_comment(self, user_id: int, reply_data: CommentReply) -> bool:
        """回复留言"""
        try:
            comment = self.get_comment_by_id(reply_data.comment_id, user_id)
            if not comment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="留言不存在"
                )
            
            # 更新回复信息
            comment.reply_content = reply_data.reply_content
            comment.replied_at = datetime.utcnow()
            comment.status = CommentStatus.REPLIED
            
            self.db.commit()
            
            logger.info(f"留言回复成功: {comment.comment_id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"回复留言时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="留言回复失败"
            )
    
    def batch_reply_comments(self, user_id: int, batch_reply_data: CommentBatchReply) -> dict:
        """批量回复留言"""
        try:
            # 获取用户的留言
            comments = self.db.query(Comment).join(Note).join(XiaohongshuAccount).filter(
                and_(
                    Comment.id.in_(batch_reply_data.comment_ids),
                    XiaohongshuAccount.user_id == user_id
                )
            ).all()
            
            if len(comments) != len(batch_reply_data.comment_ids):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="部分留言不存在或无权限访问"
                )
            
            success_count = 0
            failed_count = 0
            
            for comment in comments:
                try:
                    comment.reply_content = batch_reply_data.reply_content
                    comment.replied_at = datetime.utcnow()
                    comment.status = CommentStatus.REPLIED
                    success_count += 1
                except Exception as e:
                    logger.warning(f"批量回复失败 - 留言ID: {comment.id}, 错误: {e}")
                    failed_count += 1
            
            self.db.commit()
            
            result = {
                "total": len(batch_reply_data.comment_ids),
                "success": success_count,
                "failed": failed_count
            }
            
            logger.info(f"批量回复完成: {result}")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"批量回复时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="批量回复失败"
            )
    
    def get_comment_stats(self, user_id: int, note_id: Optional[int] = None) -> dict:
        """获取留言统计信息"""
        try:
            query = self.db.query(Comment).join(Note).join(XiaohongshuAccount).filter(
                XiaohongshuAccount.user_id == user_id
            )
            
            if note_id:
                query = query.filter(Comment.note_id == note_id)
            
            # 统计各状态的留言数量
            total_comments = query.count()
            new_comments = query.filter(Comment.status == CommentStatus.NEW).count()
            replied_comments = query.filter(Comment.status == CommentStatus.REPLIED).count()
            ignored_comments = query.filter(Comment.status == CommentStatus.IGNORED).count()
            error_comments = query.filter(Comment.status == CommentStatus.ERROR).count()
            
            # 获取最后活动时间
            last_comment = query.order_by(Comment.publish_time.desc()).first()
            last_activity = last_comment.publish_time if last_comment else None
            
            return {
                "total_comments": total_comments,
                "new_comments": new_comments,
                "replied_comments": replied_comments,
                "ignored_comments": ignored_comments,
                "error_comments": error_comments,
                "last_activity": last_activity
            }
            
        except Exception as e:
            logger.error(f"获取留言统计信息时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取统计信息失败"
            )
    
    def mark_comments_as_ignored(self, user_id: int, comment_ids: List[int]) -> dict:
        """标记留言为忽略"""
        try:
            comments = self.db.query(Comment).join(Note).join(XiaohongshuAccount).filter(
                and_(
                    Comment.id.in_(comment_ids),
                    XiaohongshuAccount.user_id == user_id
                )
            ).all()
            
            success_count = 0
            for comment in comments:
                comment.status = CommentStatus.IGNORED
                success_count += 1
            
            self.db.commit()
            
            result = {
                "total": len(comment_ids),
                "success": success_count,
                "failed": len(comment_ids) - success_count
            }
            
            logger.info(f"标记忽略完成: {result}")
            return result
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"标记忽略时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="标记忽略失败"
            )
    
    def get_pending_comments(self, user_id: int, limit: int = 50) -> List[Comment]:
        """获取待处理的留言"""
        return self.db.query(Comment).join(Note).join(XiaohongshuAccount).filter(
            and_(
                XiaohongshuAccount.user_id == user_id,
                Comment.status == CommentStatus.NEW
            )
        ).order_by(Comment.publish_time.desc()).limit(limit).all()

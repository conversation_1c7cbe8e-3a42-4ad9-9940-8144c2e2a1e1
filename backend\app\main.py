"""
小红书自动回复工具 - FastAPI 主应用
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
from .core.config import settings
from .models import create_tables
from .api.responses import success_response, error_response


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logging.info("应用启动中...")

    # 创建数据库表
    try:
        create_tables()
        logging.info("数据库表创建成功")
    except Exception as e:
        logging.error(f"数据库表创建失败: {e}")

    yield

    # 关闭时执行
    logging.info("应用关闭中...")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    description="自动化回复小红书笔记留言的工具",
    version=settings.APP_VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            message=exc.detail,
            error_code=str(exc.status_code)
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理"""
    logging.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content=error_response(
            message="服务器内部错误",
            error_code="INTERNAL_ERROR"
        ).dict()
    )


@app.get("/")
async def root():
    """根路径，返回API信息"""
    return success_response(
        data={
            "name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT,
            "docs": "/docs" if settings.DEBUG else None
        },
        message="欢迎使用小红书自动回复工具 API"
    )


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return success_response(
        data={"status": "healthy", "timestamp": "2025-07-17"},
        message="服务运行正常"
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )

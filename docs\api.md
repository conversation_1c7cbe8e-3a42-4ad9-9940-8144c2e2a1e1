# API 文档

本文档详细描述了小红书自动回复工具的所有API接口。

## 基础信息

- **Base URL**: `http://localhost:8000/api/v1`
- **认证方式**: Bearer <PERSON> (JWT)
- **Content-Type**: `application/json`

## 认证

除了注册和登录接口外，所有API都需要在请求头中包含认证令牌：

```
Authorization: Bearer <your_jwt_token>
```

## 响应格式

所有API响应都遵循统一的格式：

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 分页响应
```json
{
  "success": true,
  "message": "获取数据成功",
  "data": [
    // 数据列表
  ],
  "pagination": {
    "page": 1,
    "size": 10,
    "total": 100,
    "pages": 10
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 1. 认证接口

### 1.1 用户注册

**POST** `/auth/register`

注册新用户账号。

**请求体**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "测试用户"
}
```

**响应**:
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "full_name": "测试用户",
      "is_active": true,
      "is_superuser": false,
      "created_at": "2024-01-01T12:00:00Z"
    }
  }
}
```

### 1.2 用户登录

**POST** `/auth/login`

用户登录获取访问令牌。

**请求体** (Form Data):
```
username: testuser
password: password123
```

**响应**: 同注册接口

### 1.3 获取当前用户信息

**GET** `/auth/me`

获取当前登录用户的详细信息。

**响应**:
```json
{
  "success": true,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "full_name": "测试用户",
    "is_active": true,
    "is_superuser": false,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:30:00Z"
  }
}
```

### 1.4 修改密码

**POST** `/auth/change-password`

修改当前用户密码。

**请求体**:
```json
{
  "current_password": "oldpassword",
  "new_password": "newpassword123"
}
```

**响应**:
```json
{
  "success": true,
  "message": "密码修改成功",
  "data": null
}
```

### 1.5 刷新令牌

**POST** `/auth/refresh`

刷新访问令牌。

**响应**: 同登录接口

## 2. 小红书账号管理

### 2.1 创建小红书账号

**POST** `/xiaohongshu/accounts`

添加新的小红书账号。

**请求体**:
```json
{
  "account_name": "我的小红书账号",
  "account_id": "xiaohongshu_user_id",
  "login_phone": "***********",
  "login_email": "<EMAIL>"
}
```

**响应**:
```json
{
  "success": true,
  "message": "小红书账号创建成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "account_name": "我的小红书账号",
    "account_id": "xiaohongshu_user_id",
    "login_phone": "***********",
    "login_email": "<EMAIL>",
    "is_active": true,
    "last_login": null,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": null
  }
}
```

### 2.2 获取账号列表

**GET** `/xiaohongshu/accounts`

获取当前用户的小红书账号列表。

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 10, 最大: 100)

**响应**: 分页响应格式，data为账号列表

### 2.3 获取账号详情

**GET** `/xiaohongshu/accounts/{account_id}`

获取指定账号的详细信息。

**路径参数**:
- `account_id`: 账号ID

**响应**: 同创建账号响应

### 2.4 更新账号信息

**PUT** `/xiaohongshu/accounts/{account_id}`

更新账号信息。

**请求体**:
```json
{
  "account_name": "更新后的账号名称",
  "login_phone": "***********",
  "is_active": true
}
```

**响应**: 同创建账号响应

### 2.5 删除账号

**DELETE** `/xiaohongshu/accounts/{account_id}`

删除指定账号。

**响应**:
```json
{
  "success": true,
  "message": "账号删除成功",
  "data": null
}
```

### 2.6 更新账号Cookies

**POST** `/xiaohongshu/accounts/{account_id}/cookies`

更新账号的登录Cookies。

**请求体**:
```json
{
  "cookies": "cookie_string_here",
  "session_data": "session_data_here"
}
```

**响应**:
```json
{
  "success": true,
  "message": "Cookies更新成功",
  "data": null
}
```

### 2.7 获取账号Cookies

**GET** `/xiaohongshu/accounts/{account_id}/cookies`

获取账号的Cookies信息。

**响应**:
```json
{
  "success": true,
  "message": "获取Cookies成功",
  "data": {
    "cookies": "cookie_string_here",
    "session_data": "session_data_here",
    "last_login": "2024-01-01T12:00:00Z"
  }
}
```

### 2.8 激活账号

**POST** `/xiaohongshu/accounts/{account_id}/activate`

激活指定账号。

**响应**:
```json
{
  "success": true,
  "message": "账号激活成功",
  "data": null
}
```

### 2.9 停用账号

**POST** `/xiaohongshu/accounts/{account_id}/deactivate`

停用指定账号。

**响应**: 同激活账号响应

## 3. 笔记管理

### 3.1 创建笔记

**POST** `/notes`

添加要监控的笔记。

**请求体**:
```json
{
  "account_id": 1,
  "note_url": "https://www.xiaohongshu.com/explore/xxxxx",
  "title": "笔记标题",
  "crawl_interval": 300,
  "auto_reply_enabled": true
}
```

**响应**:
```json
{
  "success": true,
  "message": "笔记创建成功",
  "data": {
    "id": 1,
    "account_id": 1,
    "note_id": "extracted_note_id",
    "note_url": "https://www.xiaohongshu.com/explore/xxxxx",
    "title": "笔记标题",
    "content": null,
    "author_name": null,
    "author_id": null,
    "publish_time": null,
    "likes_count": 0,
    "comments_count": 0,
    "shares_count": 0,
    "status": "active",
    "crawl_interval": 300,
    "auto_reply_enabled": true,
    "last_crawled": null,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": null
  }
}
```

### 3.2 获取笔记列表

**GET** `/notes`

获取笔记列表。

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 10, 最大: 100)
- `account_id`: 账号ID (可选)
- `status`: 笔记状态 (可选: active, paused, completed, error)
- `auto_reply_enabled`: 是否启用自动回复 (可选: true, false)
- `keyword`: 搜索关键词 (可选)

**响应**: 分页响应格式，data为笔记列表

### 3.3 获取笔记详情

**GET** `/notes/{note_id}`

获取指定笔记的详细信息。

**响应**: 同创建笔记响应

### 3.4 更新笔记

**PUT** `/notes/{note_id}`

更新笔记配置。

**请求体**:
```json
{
  "title": "更新后的标题",
  "crawl_interval": 600,
  "auto_reply_enabled": false,
  "status": "paused"
}
```

**响应**: 同创建笔记响应

### 3.5 删除笔记

**DELETE** `/notes/{note_id}`

删除指定笔记。

**响应**:
```json
{
  "success": true,
  "message": "笔记删除成功",
  "data": null
}
```

### 3.6 批量操作笔记

**POST** `/notes/batch`

批量操作多个笔记。

**请求体**:
```json
{
  "note_ids": [1, 2, 3],
  "operation": "activate"
}
```

**操作类型**:
- `activate`: 激活
- `deactivate`: 停用
- `delete`: 删除

**响应**:
```json
{
  "success": true,
  "message": "批量activate操作完成",
  "data": {
    "operation": "activate",
    "total": 3,
    "success": 3,
    "failed": 0
  }
}
```

### 3.7 获取笔记统计

**GET** `/notes/{note_id}/stats`

获取笔记的统计信息。

**响应**:
```json
{
  "success": true,
  "message": "获取笔记统计成功",
  "data": {
    "note_id": 1,
    "note_title": "笔记标题",
    "note_status": "active",
    "likes_count": 100,
    "comments_count": 50,
    "shares_count": 20,
    "last_crawled": "2024-01-01T12:00:00Z",
    "comment_stats": {
      "total_comments": 50,
      "new_comments": 5,
      "replied_comments": 40,
      "ignored_comments": 3,
      "error_comments": 2,
      "last_activity": "2024-01-01T11:30:00Z"
    }
  }
}
```

## 4. 留言管理

### 4.1 获取留言列表

**GET** `/comments`

获取留言列表。

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 10, 最大: 100)
- `note_id`: 笔记ID (可选)
- `status`: 留言状态 (可选: new, replied, ignored, error)
- `author_name`: 作者名称 (可选)
- `keyword`: 搜索关键词 (可选)
- `date_from`: 开始日期 (可选)
- `date_to`: 结束日期 (可选)

**响应**:
```json
{
  "success": true,
  "message": "获取留言列表成功",
  "data": [
    {
      "id": 1,
      "note_id": 1,
      "comment_id": "comment_123",
      "content": "这是一条留言内容",
      "author_name": "用户名",
      "author_id": "user_123",
      "parent_comment_id": null,
      "publish_time": "2024-01-01T12:00:00Z",
      "status": "new",
      "reply_content": null,
      "replied_at": null,
      "created_at": "2024-01-01T12:05:00Z",
      "updated_at": null
    }
  ],
  "pagination": {
    "page": 1,
    "size": 10,
    "total": 100,
    "pages": 10
  }
}
```

### 4.2 获取留言详情

**GET** `/comments/{comment_id}`

获取指定留言的详细信息。

**响应**: 同留言列表中的单个留言格式

### 4.3 更新留言

**PUT** `/comments/{comment_id}`

更新留言信息。

**请求体**:
```json
{
  "status": "replied",
  "reply_content": "感谢您的留言！",
  "replied_at": "2024-01-01T12:30:00Z"
}
```

**响应**: 同留言详情响应

### 4.4 回复留言

**POST** `/comments/{comment_id}/reply`

回复指定留言。

**查询参数**:
- `reply_content`: 回复内容

**响应**:
```json
{
  "success": true,
  "message": "留言回复成功",
  "data": null
}
```

### 4.5 批量回复留言

**POST** `/comments/batch-reply`

批量回复多条留言。

**请求体**:
```json
{
  "comment_ids": [1, 2, 3],
  "reply_content": "感谢大家的留言！"
}
```

**响应**:
```json
{
  "success": true,
  "message": "批量回复操作完成",
  "data": {
    "total": 3,
    "success": 3,
    "failed": 0
  }
}
```

### 4.6 批量忽略留言

**POST** `/comments/batch-ignore`

批量忽略多条留言。

**请求体**:
```json
[1, 2, 3]
```

**响应**: 同批量回复响应

### 4.7 获取留言统计

**GET** `/comments/stats/overview`

获取留言统计信息。

**查询参数**:
- `note_id`: 笔记ID (可选，不传则获取全部统计)

**响应**:
```json
{
  "success": true,
  "message": "获取留言统计成功",
  "data": {
    "total_comments": 100,
    "new_comments": 10,
    "replied_comments": 80,
    "ignored_comments": 8,
    "error_comments": 2,
    "last_activity": "2024-01-01T12:00:00Z"
  }
}
```

### 4.8 获取待处理留言

**GET** `/comments/pending`

获取待处理的留言列表。

**查询参数**:
- `limit`: 返回数量 (默认: 50, 最大: 100)

**响应**: 同留言列表响应

## 5. 爬虫管理

### 5.1 启动抓取任务

**POST** `/crawler/crawl`

启动爬虫抓取任务。

**请求体**:
```json
{
  "note_ids": [1, 2, 3],
  "force_crawl": false
}
```

**参数说明**:
- `note_ids`: 指定要抓取的笔记ID列表 (可选，为空则抓取所有活跃笔记)
- `force_crawl`: 是否强制抓取，忽略间隔限制 (默认: false)

**响应**:
```json
{
  "success": true,
  "message": "爬虫抓取任务启动成功",
  "data": {
    "message": "抓取任务已启动",
    "note_ids": [1, 2, 3],
    "force_crawl": false
  }
}
```

### 5.2 获取抓取状态

**GET** `/crawler/status`

获取当前用户的爬虫抓取状态。

**响应**:
```json
{
  "success": true,
  "message": "获取爬虫状态成功",
  "data": {
    "total_notes": 10,
    "active_notes": 8,
    "pending_comments": 25,
    "recent_crawled": [
      {
        "note_id": 1,
        "title": "笔记标题",
        "last_crawled": "2024-01-01T12:00:00Z",
        "comments_count": 50
      }
    ]
  }
}
```

### 5.3 测试抓取

**POST** `/crawler/test`

测试单个笔记的抓取功能。

**查询参数**:
- `note_id`: 笔记ID

**响应**:
```json
{
  "success": true,
  "message": "测试抓取完成",
  "data": {
    "note_id": 1,
    "success": true,
    "new_comments_count": 5,
    "error_message": null,
    "crawl_time": "2024-01-01T12:00:00Z"
  }
}
```

### 5.4 获取抓取日志

**GET** `/crawler/logs`

获取爬虫抓取日志。

**查询参数**:
- `limit`: 返回数量 (默认: 50)

**响应**:
```json
{
  "success": true,
  "message": "获取爬虫日志成功",
  "data": [
    {
      "timestamp": "2024-01-01T10:00:00",
      "level": "INFO",
      "message": "开始抓取笔记: 测试笔记1",
      "note_id": 1
    },
    {
      "timestamp": "2024-01-01T10:01:00",
      "level": "SUCCESS",
      "message": "抓取完成，新增留言 5 条",
      "note_id": 1
    }
  ]
}
```

### 5.5 启动定时任务

**POST** `/crawler/schedule/start`

启动定时爬虫任务。

**响应**:
```json
{
  "success": true,
  "message": "定时爬虫任务启动成功",
  "data": {
    "status": "started"
  }
}
```

### 5.6 停止定时任务

**POST** `/crawler/schedule/stop`

停止定时爬虫任务。

**响应**:
```json
{
  "success": true,
  "message": "定时爬虫任务停止成功",
  "data": {
    "status": "stopped"
  }
}
```

### 5.7 获取定时任务状态

**GET** `/crawler/schedule/status`

获取定时任务的运行状态。

**响应**:
```json
{
  "success": true,
  "message": "获取定时任务状态成功",
  "data": {
    "is_running": false,
    "next_run_time": null,
    "last_run_time": null,
    "total_runs": 0,
    "success_runs": 0,
    "failed_runs": 0
  }
}
```

## 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| VALIDATION_ERROR | 422 | 请求参数验证失败 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| CONFLICT | 409 | 资源冲突 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## 限制说明

- API请求频率限制: 每分钟最多100次请求
- 单次批量操作最多支持50个项目
- 文件上传大小限制: 10MB
- 令牌有效期: 24小时

## 示例代码

### Python示例

```python
import requests

# 登录获取令牌
login_data = {
    "username": "testuser",
    "password": "password123"
}
response = requests.post("http://localhost:8000/api/v1/auth/login", data=login_data)
token = response.json()["data"]["access_token"]

# 使用令牌访问API
headers = {"Authorization": f"Bearer {token}"}
response = requests.get("http://localhost:8000/api/v1/notes", headers=headers)
notes = response.json()["data"]
```

### JavaScript示例

```javascript
// 登录获取令牌
const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: 'username=testuser&password=password123'
});
const loginData = await loginResponse.json();
const token = loginData.data.access_token;

// 使用令牌访问API
const notesResponse = await fetch('http://localhost:8000/api/v1/notes', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const notes = await notesResponse.json();
```
```

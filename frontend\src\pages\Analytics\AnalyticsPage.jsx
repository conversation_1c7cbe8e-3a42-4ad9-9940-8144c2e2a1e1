import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Button,
  Tabs,
  Table,
  Progress,
  Tag,
  Space,
  Typography,
  Spin,
  Empty,
} from 'antd';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  TrendingUpOutlined,
  MessageOutlined,
  RobotOutlined,
  FileTextOutlined,
  UserOutlined,
  DownloadOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const AnalyticsPage = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);
  const [analyticsData, setAnalyticsData] = useState({});
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadAnalyticsData();
  }, [dateRange]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockData = {
        overview: {
          total_comments: 1248,
          reply_rate: 87.5,
          ai_replies_generated: 892,
          efficiency_score: 94.2,
          trends: {
            comment_trend: [
              { date: '2024-01-12', count: 45 },
              { date: '2024-01-13', count: 52 },
              { date: '2024-01-14', count: 38 },
              { date: '2024-01-15', count: 61 },
              { date: '2024-01-16', count: 48 },
              { date: '2024-01-17', count: 55 },
              { date: '2024-01-18', count: 42 },
            ],
            ai_usage_trend: [
              { date: '2024-01-12', count: 32, tokens: 4800 },
              { date: '2024-01-13', count: 38, tokens: 5700 },
              { date: '2024-01-14', count: 28, tokens: 4200 },
              { date: '2024-01-15', count: 45, tokens: 6750 },
              { date: '2024-01-16', count: 35, tokens: 5250 },
              { date: '2024-01-17', count: 41, tokens: 6150 },
              { date: '2024-01-18', count: 31, tokens: 4650 },
            ]
          }
        },
        comments: {
          total_comments: 1248,
          status_distribution: {
            'pending': 156,
            'replied': 1092
          },
          sentiment_distribution: {
            'positive': 687,
            'neutral': 423,
            'negative': 138
          },
          reply_rate: 87.5,
          avg_reply_time_hours: 2.3
        },
        ai: {
          total_ai_replies: 892,
          model_usage: {
            'gpt-3.5-turbo': 654,
            'gpt-4': 238
          },
          total_tokens_used: 134567,
          avg_tokens_per_reply: 150.8,
          avg_quality_rating: 4.2,
          user_feedback: {
            'good': 567,
            'neutral': 234,
            'bad': 91
          }
        },
        templates: {
          total_templates: 24,
          active_templates: 18,
          ai_generated_templates: 12,
          category_statistics: {
            '感谢类': { count: 6, total_usage: 234, avg_success_rate: 92 },
            '咨询类': { count: 8, total_usage: 456, avg_success_rate: 88 },
            '购买类': { count: 5, total_usage: 189, avg_success_rate: 95 },
            '问候类': { count: 5, total_usage: 123, avg_success_rate: 85 }
          }
        }
      };
      
      setAnalyticsData(mockData);
    } catch (error) {
      console.error('加载分析数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
  };

  const handleExport = (format) => {
    console.log(`导出${format}格式数据`);
  };

  // 图表颜色配置
  const chartColors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#fa8c16'];

  // 概览页面
  const OverviewTab = () => (
    <div>
      {/* 核心指标卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总留言数"
              value={analyticsData.overview?.total_comments}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="回复率"
              value={analyticsData.overview?.reply_rate}
              suffix="%"
              prefix={<TrendingUpOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="AI回复数"
              value={analyticsData.overview?.ai_replies_generated}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="效率分数"
              value={analyticsData.overview?.efficiency_score}
              suffix="/100"
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 趋势图表 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="留言趋势" extra={<LineChartOutlined />}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData.overview?.trends?.comment_trend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="count" stroke="#1890ff" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="AI使用趋势" extra={<RobotOutlined />}>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={analyticsData.overview?.trends?.ai_usage_trend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="count" stackId="1" stroke="#722ed1" fill="#722ed1" />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>
    </div>
  );

  // 留言分析页面
  const CommentsTab = () => {
    const statusData = Object.entries(analyticsData.comments?.status_distribution || {}).map(([key, value]) => ({
      name: key === 'pending' ? '待回复' : '已回复',
      value,
      color: key === 'pending' ? '#faad14' : '#52c41a'
    }));

    const sentimentData = Object.entries(analyticsData.comments?.sentiment_distribution || {}).map(([key, value]) => ({
      name: key === 'positive' ? '正面' : key === 'neutral' ? '中性' : '负面',
      value,
      color: key === 'positive' ? '#52c41a' : key === 'neutral' ? '#1890ff' : '#f5222d'
    }));

    return (
      <div>
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Card>
              <Statistic
                title="平均回复时间"
                value={analyticsData.comments?.avg_reply_time_hours}
                suffix="小时"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="回复率"
                value={analyticsData.comments?.reply_rate}
                suffix="%"
                valueStyle={{ color: '#52c41a' }}
              />
              <Progress 
                percent={analyticsData.comments?.reply_rate} 
                size="small" 
                showInfo={false}
                style={{ marginTop: 8 }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="总留言数"
                value={analyticsData.comments?.total_comments}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Card title="回复状态分布">
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="情感分布">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={sentimentData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#1890ff" />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // AI分析页面
  const AITab = () => {
    const modelData = Object.entries(analyticsData.ai?.model_usage || {}).map(([key, value]) => ({
      name: key,
      value
    }));

    const feedbackData = Object.entries(analyticsData.ai?.user_feedback || {}).map(([key, value]) => ({
      name: key === 'good' ? '好评' : key === 'neutral' ? '中性' : '差评',
      value,
      color: key === 'good' ? '#52c41a' : key === 'neutral' ? '#1890ff' : '#f5222d'
    }));

    return (
      <div>
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="AI回复总数"
                value={analyticsData.ai?.total_ai_replies}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Token使用量"
                value={analyticsData.ai?.total_tokens_used}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均质量评分"
                value={analyticsData.ai?.avg_quality_rating}
                suffix="/5"
                precision={1}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均Token数"
                value={analyticsData.ai?.avg_tokens_per_reply}
                precision={1}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Card title="模型使用分布">
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={modelData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {modelData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="用户反馈分布">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={feedbackData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value">
                    {feedbackData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 模板分析页面
  const TemplatesTab = () => {
    const categoryData = Object.entries(analyticsData.templates?.category_statistics || {}).map(([key, value]) => ({
      category: key,
      count: value.count,
      usage: value.total_usage,
      success_rate: value.avg_success_rate
    }));

    const columns = [
      {
        title: '分类',
        dataIndex: 'category',
        key: 'category',
      },
      {
        title: '模板数量',
        dataIndex: 'count',
        key: 'count',
      },
      {
        title: '使用次数',
        dataIndex: 'usage',
        key: 'usage',
      },
      {
        title: '成功率',
        dataIndex: 'success_rate',
        key: 'success_rate',
        render: (rate) => (
          <div>
            <Text>{rate}%</Text>
            <Progress 
              percent={rate} 
              size="small" 
              showInfo={false}
              style={{ marginTop: 4 }}
            />
          </div>
        ),
      },
    ];

    return (
      <div>
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Card>
              <Statistic
                title="总模板数"
                value={analyticsData.templates?.total_templates}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="启用模板"
                value={analyticsData.templates?.active_templates}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="AI生成模板"
                value={analyticsData.templates?.ai_generated_templates}
                prefix={<RobotOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Card title="模板分类统计">
          <Table
            columns={columns}
            dataSource={categoryData}
            rowKey="category"
            pagination={false}
          />
        </Card>
      </div>
    );
  };

  return (
    <div className="analytics-page">
      <div className="page-header">
        <Title level={2}>数据分析</Title>
        <p>深入了解留言处理和AI使用情况</p>
      </div>

      {/* 控制面板 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Space>
              <Text strong>时间范围:</Text>
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                format="YYYY-MM-DD"
              />
            </Space>
          </Col>
          <Col span={16} style={{ textAlign: 'right' }}>
            <Space>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={loadAnalyticsData}
                loading={loading}
              >
                刷新数据
              </Button>
              <Select
                defaultValue="json"
                style={{ width: 120 }}
                onChange={handleExport}
              >
                <Option value="json">JSON</Option>
                <Option value="excel">Excel</Option>
                <Option value="pdf">PDF</Option>
              </Select>
              <Button type="primary" icon={<DownloadOutlined />}>
                导出报告
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 分析内容 */}
      <Spin spinning={loading}>
        {Object.keys(analyticsData).length === 0 && !loading ? (
          <Card>
            <Empty description="暂无数据" />
          </Card>
        ) : (
          <Tabs activeKey={activeTab} onChange={setActiveTab} type="card">
            <TabPane tab={<span><BarChartOutlined />概览</span>} key="overview">
              <OverviewTab />
            </TabPane>
            <TabPane tab={<span><MessageOutlined />留言分析</span>} key="comments">
              <CommentsTab />
            </TabPane>
            <TabPane tab={<span><RobotOutlined />AI分析</span>} key="ai">
              <AITab />
            </TabPane>
            <TabPane tab={<span><FileTextOutlined />模板分析</span>} key="templates">
              <TemplatesTab />
            </TabPane>
          </Tabs>
        )}
      </Spin>
    </div>
  );
};

export default AnalyticsPage;

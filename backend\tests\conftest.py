"""
测试配置文件
"""
import pytest
import asyncio
from typing import Generator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.models.database import Base
from app.core.deps import get_db
from app.core.security import create_access_token
from app.models.user import User
from app.models.xiaohongshu_account import <PERSON><PERSON>shuAccount
from app.models.note import Note
from app.models.comment import Comment
from app.models.reply_template import ReplyTemplate, AIConfig

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """创建测试数据库会话"""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """创建测试客户端"""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
def test_user(db_session):
    """创建测试用户"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # secret
        is_active=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_user_token(test_user):
    """创建测试用户token"""
    return create_access_token(data={"sub": str(test_user.id)})


@pytest.fixture
def auth_headers(test_user_token):
    """创建认证头"""
    return {"Authorization": f"Bearer {test_user_token}"}


@pytest.fixture
def test_account(db_session, test_user):
    """创建测试小红书账号"""
    account = XiaohongshuAccount(
        user_id=test_user.id,
        account_name="测试账号",
        account_id="test_account_123",
        cookies="test_cookies",
        is_active=True
    )
    db_session.add(account)
    db_session.commit()
    db_session.refresh(account)
    return account


@pytest.fixture
def test_note(db_session, test_account):
    """创建测试笔记"""
    note = Note(
        account_id=test_account.id,
        note_id="test_note_123",
        title="测试笔记",
        content="这是一个测试笔记",
        url="https://www.xiaohongshu.com/explore/test_note_123",
        like_count=100,
        comment_count=50,
        share_count=20
    )
    db_session.add(note)
    db_session.commit()
    db_session.refresh(note)
    return note


@pytest.fixture
def test_comment(db_session, test_note, test_user):
    """创建测试留言"""
    comment = Comment(
        note_id=test_note.id,
        user_id=test_user.id,
        comment_id="test_comment_123",
        user_name="测试用户",
        content="这是一个测试留言",
        reply_status="pending"
    )
    db_session.add(comment)
    db_session.commit()
    db_session.refresh(comment)
    return comment


@pytest.fixture
def test_template(db_session, test_user):
    """创建测试回复模板"""
    template = ReplyTemplate(
        user_id=test_user.id,
        name="测试模板",
        category="测试类",
        content="谢谢你的关注！{emoji}",
        variables=["emoji"],
        tags=["测试", "回复"],
        usage_count=0,
        success_rate=0,
        is_active=True
    )
    db_session.add(template)
    db_session.commit()
    db_session.refresh(template)
    return template


@pytest.fixture
def test_ai_config(db_session, test_user):
    """创建测试AI配置"""
    config = AIConfig(
        user_id=test_user.id,
        api_provider="openai",
        default_model="gpt-3.5-turbo",
        temperature="0.7",
        max_tokens=150,
        reply_language="zh",
        reply_tone="friendly",
        include_emoji=True,
        content_filter=True,
        max_daily_requests=1000,
        current_daily_requests=0
    )
    db_session.add(config)
    db_session.commit()
    db_session.refresh(config)
    return config


# 测试数据工厂
class TestDataFactory:
    """测试数据工厂"""
    
    @staticmethod
    def create_user(db_session, **kwargs):
        """创建用户"""
        default_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",
            "is_active": True
        }
        default_data.update(kwargs)
        
        user = User(**default_data)
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user
    
    @staticmethod
    def create_account(db_session, user_id, **kwargs):
        """创建小红书账号"""
        default_data = {
            "user_id": user_id,
            "account_name": "测试账号",
            "account_id": "test_account",
            "cookies": "test_cookies",
            "is_active": True
        }
        default_data.update(kwargs)
        
        account = XiaohongshuAccount(**default_data)
        db_session.add(account)
        db_session.commit()
        db_session.refresh(account)
        return account
    
    @staticmethod
    def create_note(db_session, account_id, **kwargs):
        """创建笔记"""
        default_data = {
            "account_id": account_id,
            "note_id": "test_note",
            "title": "测试笔记",
            "content": "测试内容",
            "url": "https://test.com",
            "like_count": 0,
            "comment_count": 0,
            "share_count": 0
        }
        default_data.update(kwargs)
        
        note = Note(**default_data)
        db_session.add(note)
        db_session.commit()
        db_session.refresh(note)
        return note
    
    @staticmethod
    def create_comment(db_session, note_id, user_id, **kwargs):
        """创建留言"""
        default_data = {
            "note_id": note_id,
            "user_id": user_id,
            "comment_id": "test_comment",
            "user_name": "测试用户",
            "content": "测试留言",
            "reply_status": "pending"
        }
        default_data.update(kwargs)
        
        comment = Comment(**default_data)
        db_session.add(comment)
        db_session.commit()
        db_session.refresh(comment)
        return comment


@pytest.fixture
def test_factory():
    """测试数据工厂实例"""
    return TestDataFactory

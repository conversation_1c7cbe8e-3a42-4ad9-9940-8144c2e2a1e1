import axios from 'axios';
import { message } from 'antd';

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证令牌
    const token = localStorage.getItem('auth-storage');
    if (token) {
      try {
        const authData = JSON.parse(token);
        if (authData.state?.token) {
          config.headers.Authorization = `Bearer ${authData.state.token}`;
        }
      } catch (error) {
        console.error('解析认证令牌失败:', error);
      }
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() };
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 计算请求耗时
    const endTime = new Date();
    const duration = endTime - response.config.metadata.startTime;
    
    // 开发环境下打印请求信息
    if (import.meta.env.DEV) {
      console.log(`API请求: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`);
    }
    
    // 检查响应格式
    if (response.data && typeof response.data === 'object') {
      // 如果是标准响应格式，直接返回
      if (response.data.hasOwnProperty('success')) {
        return response.data;
      }
      // 否则包装成标准格式
      return {
        success: true,
        data: response.data,
        message: '请求成功',
      };
    }
    
    return response.data;
  },
  (error) => {
    // 计算请求耗时
    const endTime = new Date();
    const duration = endTime - error.config?.metadata?.startTime;
    
    // 开发环境下打印错误信息
    if (import.meta.env.DEV) {
      console.error(`API错误: ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${duration}ms`, error);
    }
    
    // 处理不同类型的错误
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // 未授权，清除本地认证信息
          localStorage.removeItem('auth-storage');
          message.error('登录已过期，请重新登录');
          // 跳转到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
          break;
          
        case 403:
          message.error('权限不足，无法访问该资源');
          break;
          
        case 404:
          message.error('请求的资源不存在');
          break;
          
        case 422:
          // 数据验证错误
          const validationError = data?.detail || '数据验证失败';
          if (Array.isArray(validationError)) {
            // 处理多个验证错误
            const errorMessages = validationError.map(err => err.msg || err.message).join(', ');
            message.error(`数据验证失败: ${errorMessages}`);
          } else {
            message.error(validationError);
          }
          break;
          
        case 429:
          message.error('请求过于频繁，请稍后再试');
          break;
          
        case 500:
          message.error('服务器内部错误，请稍后重试');
          break;
          
        default:
          const errorMessage = data?.message || data?.detail || `请求失败 (${status})`;
          message.error(errorMessage);
      }
      
      // 返回标准错误格式
      return Promise.reject({
        success: false,
        error: data?.message || data?.detail || '请求失败',
        status,
        response: error.response,
      });
    } else if (error.request) {
      // 网络错误
      message.error('网络连接失败，请检查网络设置');
      return Promise.reject({
        success: false,
        error: '网络连接失败',
        type: 'network',
      });
    } else {
      // 其他错误
      message.error('请求配置错误');
      return Promise.reject({
        success: false,
        error: error.message || '请求失败',
        type: 'config',
      });
    }
  }
);

// 设置认证令牌
export const setAuthToken = (token) => {
  if (token) {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    delete api.defaults.headers.common['Authorization'];
  }
};

// 清除认证令牌
export const clearAuthToken = () => {
  delete api.defaults.headers.common['Authorization'];
};

// 通用请求方法
export const request = {
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
};

export default api;

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  InputNumber,
  message,
  Typography,
  Divider,
  Space,
  Alert,
  Tabs,
  Upload,
  Avatar,
  Modal,
  List,
  Tag,
  Row,
  Col,
} from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  BellOutlined,
  SecurityScanOutlined,
  ExportOutlined,
  ImportOutlined,
  UploadOutlined,
  SaveOutlined,
  ReloadOutlined,
  DeleteOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '../../stores/authStore';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const SettingsPage = () => {
  const [loading, setLoading] = useState(false);
  const [profileForm] = Form.useForm();
  const [systemForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const { user } = useAuthStore();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    // 加载用户设置
    profileForm.setFieldsValue({
      username: user?.username || '',
      email: user?.email || '',
      full_name: user?.full_name || '',
      phone: '',
      bio: '',
    });

    // 加载系统设置
    systemForm.setFieldsValue({
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      auto_save: true,
      page_size: 10,
      enable_sound: true,
    });

    // 加载通知设置
    notificationForm.setFieldsValue({
      email_notifications: true,
      browser_notifications: true,
      new_comment_notify: true,
      reply_success_notify: false,
      crawler_error_notify: true,
      daily_report: true,
    });

    // 加载安全设置
    securityForm.setFieldsValue({
      two_factor_auth: false,
      login_alerts: true,
      session_timeout: 1440,
      password_expiry: 90,
    });
  };

  // 保存个人资料
  const handleSaveProfile = async (values) => {
    setLoading(true);
    try {
      // 这里应该调用API保存个人资料
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('个人资料保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 保存系统设置
  const handleSaveSystem = async (values) => {
    setLoading(true);
    try {
      // 这里应该调用API保存系统设置
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('系统设置保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 保存通知设置
  const handleSaveNotification = async (values) => {
    setLoading(true);
    try {
      // 这里应该调用API保存通知设置
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('通知设置保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 保存安全设置
  const handleSaveSecurity = async (values) => {
    setLoading(true);
    try {
      // 这里应该调用API保存安全设置
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('安全设置保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 修改密码
  const handleChangePassword = () => {
    Modal.confirm({
      title: '修改密码',
      content: (
        <Form layout="vertical">
          <Form.Item
            name="current_password"
            label="当前密码"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>
          <Form.Item
            name="new_password"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>
          <Form.Item
            name="confirm_password"
            label="确认密码"
            rules={[{ required: true, message: '请确认新密码' }]}
          >
            <Input.Password placeholder="请再次输入新密码" />
          </Form.Item>
        </Form>
      ),
      onOk: () => {
        message.success('密码修改成功');
      },
    });
  };

  // 导出数据
  const handleExportData = () => {
    Modal.confirm({
      title: '导出数据',
      content: '确定要导出所有数据吗？这可能需要一些时间。',
      onOk: () => {
        message.success('数据导出已开始，完成后将通过邮件发送下载链接');
      },
    });
  };

  // 导入数据
  const handleImportData = () => {
    Modal.warning({
      title: '导入数据',
      content: '数据导入功能正在开发中，敬请期待。',
    });
  };

  // 清除缓存
  const handleClearCache = () => {
    Modal.confirm({
      title: '清除缓存',
      content: '确定要清除所有缓存数据吗？这将提高系统性能但可能影响加载速度。',
      onOk: () => {
        message.success('缓存清除成功');
      },
    });
  };

  // 头像上传
  const handleAvatarUpload = (info) => {
    if (info.file.status === 'done') {
      message.success('头像上传成功');
    } else if (info.file.status === 'error') {
      message.error('头像上传失败');
    }
  };

  return (
    <div className="settings-page">
      <div className="page-header">
        <Title level={2}>系统设置</Title>
        <p>管理个人资料、系统偏好和安全配置</p>
      </div>

      <Tabs defaultActiveKey="profile" type="card">
        {/* 个人资料 */}
        <TabPane tab={<span><UserOutlined />个人资料</span>} key="profile">
          <Card>
            <Form
              form={profileForm}
              layout="vertical"
              onFinish={handleSaveProfile}
            >
              <Row gutter={24}>
                <Col span={6}>
                  <div style={{ textAlign: 'center' }}>
                    <Avatar size={120} icon={<UserOutlined />} style={{ marginBottom: 16 }} />
                    <Upload
                      showUploadList={false}
                      onChange={handleAvatarUpload}
                      beforeUpload={() => false}
                    >
                      <Button icon={<UploadOutlined />}>更换头像</Button>
                    </Upload>
                  </div>
                </Col>
                <Col span={18}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="username"
                        label="用户名"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input placeholder="请输入用户名" disabled />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="email"
                        label="邮箱地址"
                        rules={[
                          { required: true, message: '请输入邮箱地址' },
                          { type: 'email', message: '请输入有效的邮箱地址' },
                        ]}
                      >
                        <Input placeholder="请输入邮箱地址" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="full_name"
                        label="姓名"
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input placeholder="请输入姓名" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="phone"
                        label="手机号码"
                        rules={[{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }]}
                      >
                        <Input placeholder="请输入手机号码" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item
                    name="bio"
                    label="个人简介"
                  >
                    <TextArea
                      rows={3}
                      placeholder="请输入个人简介"
                      maxLength={200}
                      showCount
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />
              
              <Space>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                  保存资料
                </Button>
                <Button onClick={handleChangePassword} icon={<SecurityScanOutlined />}>
                  修改密码
                </Button>
              </Space>
            </Form>
          </Card>
        </TabPane>

        {/* 系统设置 */}
        <TabPane tab={<span><SettingOutlined />系统设置</span>} key="system">
          <Card>
            <Form
              form={systemForm}
              layout="vertical"
              onFinish={handleSaveSystem}
            >
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="theme" label="主题模式">
                    <Select>
                      <Option value="light">浅色主题</Option>
                      <Option value="dark">深色主题</Option>
                      <Option value="auto">跟随系统</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="language" label="界面语言">
                    <Select>
                      <Option value="zh-CN">简体中文</Option>
                      <Option value="en-US">English</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="timezone" label="时区设置">
                    <Select>
                      <Option value="Asia/Shanghai">北京时间 (UTC+8)</Option>
                      <Option value="America/New_York">纽约时间 (UTC-5)</Option>
                      <Option value="Europe/London">伦敦时间 (UTC+0)</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="page_size" label="每页显示条数">
                    <Select>
                      <Option value={10}>10条</Option>
                      <Option value={20}>20条</Option>
                      <Option value={50}>50条</Option>
                      <Option value={100}>100条</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="auto_save" label="自动保存" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="enable_sound" label="启用提示音" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Alert
                message="系统优化"
                description="定期清理缓存可以提高系统性能，建议每周清理一次。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
                action={
                  <Button size="small" onClick={handleClearCache}>
                    清除缓存
                  </Button>
                }
              />

              <Space>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                  保存设置
                </Button>
                <Button onClick={loadSettings} icon={<ReloadOutlined />}>
                  重置
                </Button>
              </Space>
            </Form>
          </Card>
        </TabPane>

        {/* 通知设置 */}
        <TabPane tab={<span><BellOutlined />通知设置</span>} key="notification">
          <Card>
            <Form
              form={notificationForm}
              layout="vertical"
              onFinish={handleSaveNotification}
            >
              <Title level={4}>通知方式</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="email_notifications" label="邮件通知" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="browser_notifications" label="浏览器通知" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Title level={4}>通知内容</Title>
              <List
                dataSource={[
                  { key: 'new_comment_notify', label: '新留言通知', description: '收到新留言时发送通知' },
                  { key: 'reply_success_notify', label: '回复成功通知', description: '成功回复留言时发送通知' },
                  { key: 'crawler_error_notify', label: '爬虫错误通知', description: '爬虫出现错误时发送通知' },
                  { key: 'daily_report', label: '每日报告', description: '每天发送数据统计报告' },
                ]}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      title={item.label}
                      description={item.description}
                    />
                    <Form.Item name={item.key} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </List.Item>
                )}
              />

              <Divider />

              <Space>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                  保存设置
                </Button>
              </Space>
            </Form>
          </Card>
        </TabPane>

        {/* 安全设置 */}
        <TabPane tab={<span><SecurityScanOutlined />安全设置</span>} key="security">
          <Card>
            <Form
              form={securityForm}
              layout="vertical"
              onFinish={handleSaveSecurity}
            >
              <Title level={4}>账号安全</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="two_factor_auth" label="双因素认证" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="login_alerts" label="登录提醒" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="session_timeout" label="会话超时(分钟)">
                    <InputNumber
                      min={30}
                      max={10080}
                      style={{ width: '100%' }}
                      placeholder="30-10080分钟"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="password_expiry" label="密码过期(天)">
                    <InputNumber
                      min={30}
                      max={365}
                      style={{ width: '100%' }}
                      placeholder="30-365天"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Alert
                message="安全提醒"
                description="建议启用双因素认证以提高账号安全性，定期修改密码。"
                type="warning"
                showIcon
                style={{ marginBottom: 16 }}
              />

              <Space>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                  保存设置
                </Button>
              </Space>
            </Form>
          </Card>
        </TabPane>

        {/* 数据管理 */}
        <TabPane tab={<span><ExportOutlined />数据管理</span>} key="data">
          <Card>
            <Title level={4}>数据导入导出</Title>
            <List
              dataSource={[
                {
                  title: '导出数据',
                  description: '导出所有账号、笔记和留言数据',
                  action: (
                    <Button icon={<DownloadOutlined />} onClick={handleExportData}>
                      导出
                    </Button>
                  ),
                },
                {
                  title: '导入数据',
                  description: '从备份文件导入数据',
                  action: (
                    <Button icon={<ImportOutlined />} onClick={handleImportData}>
                      导入
                    </Button>
                  ),
                },
                {
                  title: '清空数据',
                  description: '清空所有数据（不可恢复）',
                  action: (
                    <Button danger icon={<DeleteOutlined />}>
                      清空
                    </Button>
                  ),
                },
              ]}
              renderItem={(item) => (
                <List.Item actions={[item.action]}>
                  <List.Item.Meta
                    title={item.title}
                    description={item.description}
                  />
                </List.Item>
              )}
            />

            <Divider />

            <Alert
              message="数据安全"
              description="建议定期备份数据，清空数据操作不可恢复，请谨慎操作。"
              type="error"
              showIcon
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SettingsPage;

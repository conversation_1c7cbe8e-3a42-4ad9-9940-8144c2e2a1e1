import re
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from loguru import logger

from ..models.comment import Comment
from ..models.reply_template import AIRule, ReplyTemplate
from ..models.note import Note
from ..services.ai_service import AIService
from ..services.websocket_manager import notification_service


class AutomationEngine:
    """自动化规则引擎"""
    
    def __init__(self, db: Session, user_id: int):
        self.db = db
        self.user_id = user_id
        self.ai_service = AIService(db, user_id)
    
    async def process_new_comment(self, comment: Comment) -> Dict:
        """处理新留言，应用自动化规则"""
        try:
            # 获取活跃的规则，按优先级排序
            rules = self.db.query(AIRule).filter(
                AIRule.user_id == self.user_id,
                AIRule.is_active == True
            ).order_by(AIRule.priority.desc()).all()
            
            results = {
                "comment_id": comment.id,
                "matched_rules": [],
                "actions_taken": [],
                "auto_reply_generated": False,
                "auto_reply_sent": False
            }
            
            for rule in rules:
                # 检查规则是否匹配
                if await self._check_rule_match(rule, comment):
                    results["matched_rules"].append({
                        "rule_id": rule.id,
                        "rule_name": rule.name,
                        "priority": rule.priority
                    })
                    
                    # 执行规则动作
                    actions = await self._execute_rule_actions(rule, comment)
                    results["actions_taken"].extend(actions)
                    
                    # 如果是自动回复规则且已生成回复，停止处理其他规则
                    if rule.auto_reply and actions:
                        for action in actions:
                            if action["type"] == "auto_reply":
                                results["auto_reply_generated"] = True
                                if action.get("sent"):
                                    results["auto_reply_sent"] = True
                                break
                        break
            
            return results
            
        except Exception as e:
            logger.error(f"自动化处理失败: {str(e)}")
            return {
                "comment_id": comment.id,
                "error": str(e),
                "matched_rules": [],
                "actions_taken": []
            }
    
    async def _check_rule_match(self, rule: AIRule, comment: Comment) -> bool:
        """检查规则是否匹配留言"""
        
        # 检查每日使用限制
        if rule.daily_limit and rule.usage_count_today >= rule.daily_limit:
            return False
        
        # 关键词匹配
        if rule.keywords:
            if not self._check_keyword_match(rule.keywords, comment.content):
                return False
        
        # 情感过滤
        if rule.sentiment_filter:
            if not self._check_sentiment_filter(rule.sentiment_filter, comment):
                return False
        
        # 用户过滤
        if rule.user_filter:
            if not self._check_user_filter(rule.user_filter, comment):
                return False
        
        # 时间过滤
        if rule.time_filter:
            if not self._check_time_filter(rule.time_filter, comment):
                return False
        
        return True
    
    def _check_keyword_match(self, keywords: List[str], content: str) -> bool:
        """检查关键词匹配"""
        content_lower = content.lower()
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            
            # 支持正则表达式
            if keyword.startswith('/') and keyword.endswith('/'):
                pattern = keyword[1:-1]
                try:
                    if re.search(pattern, content, re.IGNORECASE):
                        return True
                except re.error:
                    # 如果正则表达式无效，按普通字符串匹配
                    if keyword_lower in content_lower:
                        return True
            else:
                # 普通字符串匹配
                if keyword_lower in content_lower:
                    return True
        
        return False
    
    def _check_sentiment_filter(self, sentiment_filter: Dict, comment: Comment) -> bool:
        """检查情感过滤条件"""
        if not comment.sentiment:
            return sentiment_filter.get("allow_unknown", True)
        
        allowed_sentiments = sentiment_filter.get("allowed", ["positive", "neutral", "negative"])
        return comment.sentiment in allowed_sentiments
    
    def _check_user_filter(self, user_filter: Dict, comment: Comment) -> bool:
        """检查用户过滤条件"""
        # 检查用户名黑名单
        if "blacklist" in user_filter:
            blacklist = user_filter["blacklist"]
            if comment.user_name in blacklist:
                return False
        
        # 检查用户名白名单
        if "whitelist" in user_filter:
            whitelist = user_filter["whitelist"]
            if comment.user_name not in whitelist:
                return False
        
        # 检查是否为新用户
        if user_filter.get("new_users_only", False):
            # 这里可以添加检查用户是否为新用户的逻辑
            pass
        
        return True
    
    def _check_time_filter(self, time_filter: Dict, comment: Comment) -> bool:
        """检查时间过滤条件"""
        now = datetime.now()
        comment_time = comment.created_at
        
        # 检查时间范围
        if "hours" in time_filter:
            allowed_hours = time_filter["hours"]
            if comment_time.hour not in allowed_hours:
                return False
        
        # 检查星期
        if "weekdays" in time_filter:
            allowed_weekdays = time_filter["weekdays"]
            if comment_time.weekday() not in allowed_weekdays:
                return False
        
        # 检查时间间隔
        if "min_interval_minutes" in time_filter:
            min_interval = time_filter["min_interval_minutes"]
            # 检查同一用户的最近留言时间
            recent_comment = self.db.query(Comment).filter(
                Comment.user_name == comment.user_name,
                Comment.created_at > comment_time - timedelta(minutes=min_interval),
                Comment.id != comment.id
            ).first()
            
            if recent_comment:
                return False
        
        return True
    
    async def _execute_rule_actions(self, rule: AIRule, comment: Comment) -> List[Dict]:
        """执行规则动作"""
        actions = []
        
        try:
            # 更新规则使用次数
            rule.usage_count_today += 1
            self.db.commit()
            
            # 自动回复
            if rule.auto_reply:
                action_result = await self._execute_auto_reply(rule, comment)
                actions.append(action_result)
            
            # 发送通知
            await self._send_rule_notification(rule, comment, actions)
            
        except Exception as e:
            logger.error(f"执行规则动作失败: {str(e)}")
            actions.append({
                "type": "error",
                "error": str(e)
            })
        
        return actions
    
    async def _execute_auto_reply(self, rule: AIRule, comment: Comment) -> Dict:
        """执行自动回复"""
        try:
            # 构建回复上下文
            context = {
                "rule_name": rule.name,
                "note_id": comment.note_id
            }
            
            # 获取笔记信息
            note = self.db.query(Note).filter(Note.id == comment.note_id).first()
            if note:
                context["note_title"] = note.title
                context["note_category"] = getattr(note, 'category', None)
            
            # 选择模板
            template_id = None
            if rule.template_ids:
                # 随机选择一个模板或使用第一个
                template_id = rule.template_ids[0]
            
            # 生成AI回复
            ai_result = await self.ai_service.generate_reply(
                comment_content=comment.content,
                context=context,
                template_id=template_id
            )
            
            if ai_result["success"]:
                reply_content = ai_result["reply"]
                
                # 如果不需要审核，直接发送回复
                if not rule.require_approval:
                    # 更新留言状态
                    comment.reply_status = "replied"
                    comment.reply_content = reply_content
                    comment.reply_time = datetime.now()
                    self.db.commit()
                    
                    return {
                        "type": "auto_reply",
                        "reply_content": reply_content,
                        "sent": True,
                        "ai_reply_id": ai_result.get("ai_reply_id")
                    }
                else:
                    # 需要审核，保存为待审核状态
                    return {
                        "type": "auto_reply",
                        "reply_content": reply_content,
                        "sent": False,
                        "requires_approval": True,
                        "ai_reply_id": ai_result.get("ai_reply_id")
                    }
            else:
                return {
                    "type": "auto_reply",
                    "error": ai_result["error"],
                    "sent": False
                }
                
        except Exception as e:
            logger.error(f"自动回复执行失败: {str(e)}")
            return {
                "type": "auto_reply",
                "error": str(e),
                "sent": False
            }
    
    async def _send_rule_notification(self, rule: AIRule, comment: Comment, actions: List[Dict]):
        """发送规则执行通知"""
        try:
            notification_data = {
                "rule_name": rule.name,
                "comment_id": comment.id,
                "comment_content": comment.content[:100] + "..." if len(comment.content) > 100 else comment.content,
                "actions_taken": len(actions),
                "auto_reply_sent": any(action.get("sent", False) for action in actions)
            }
            
            await notification_service.send_system_notification(
                f"自动化规则 '{rule.name}' 已执行",
                user_ids=[self.user_id]
            )
            
        except Exception as e:
            logger.error(f"发送规则通知失败: {str(e)}")
    
    async def batch_process_comments(self, comment_ids: List[int]) -> Dict:
        """批量处理留言"""
        results = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "details": []
        }
        
        for comment_id in comment_ids:
            comment = self.db.query(Comment).filter(
                Comment.id == comment_id,
                Comment.user_id == self.user_id
            ).first()
            
            if comment:
                try:
                    result = await self.process_new_comment(comment)
                    results["details"].append(result)
                    results["successful"] += 1
                except Exception as e:
                    results["details"].append({
                        "comment_id": comment_id,
                        "error": str(e)
                    })
                    results["failed"] += 1
            else:
                results["details"].append({
                    "comment_id": comment_id,
                    "error": "留言不存在"
                })
                results["failed"] += 1
            
            results["total_processed"] += 1
            
            # 添加延迟避免过载
            await asyncio.sleep(0.1)
        
        return results
    
    def get_rule_statistics(self, rule_id: int, days: int = 30) -> Dict:
        """获取规则统计信息"""
        rule = self.db.query(AIRule).filter(
            AIRule.id == rule_id,
            AIRule.user_id == self.user_id
        ).first()
        
        if not rule:
            return {"error": "规则不存在"}
        
        # 这里可以添加更详细的统计逻辑
        return {
            "rule_id": rule.id,
            "rule_name": rule.name,
            "usage_count_today": rule.usage_count_today,
            "daily_limit": rule.daily_limit,
            "is_active": rule.is_active
        }
    
    async def test_rule(self, rule_id: int, test_content: str) -> Dict:
        """测试规则匹配"""
        rule = self.db.query(AIRule).filter(
            AIRule.id == rule_id,
            AIRule.user_id == self.user_id
        ).first()
        
        if not rule:
            return {"error": "规则不存在"}
        
        # 创建测试留言对象
        test_comment = Comment(
            content=test_content,
            user_name="测试用户",
            created_at=datetime.now()
        )
        
        # 测试匹配
        matches = await self._check_rule_match(rule, test_comment)
        
        return {
            "rule_id": rule.id,
            "rule_name": rule.name,
            "test_content": test_content,
            "matches": matches,
            "match_details": {
                "keywords_match": self._check_keyword_match(rule.keywords or [], test_content) if rule.keywords else None,
                "sentiment_filter": rule.sentiment_filter,
                "user_filter": rule.user_filter,
                "time_filter": rule.time_filter
            }
        }

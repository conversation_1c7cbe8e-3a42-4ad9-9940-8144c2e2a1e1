"""
API v1版本路由
"""
from fastapi import APIRouter
from .auth import router as auth_router

api_router = APIRouter()

# 注册认证路由
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])

# 后续会添加更多路由
# api_router.include_router(accounts_router, prefix="/accounts", tags=["账号管理"])
# api_router.include_router(notes_router, prefix="/notes", tags=["笔记管理"])
# api_router.include_router(comments_router, prefix="/comments", tags=["留言管理"])

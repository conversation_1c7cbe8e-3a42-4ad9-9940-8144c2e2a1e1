"""
API v1版本路由
"""
from fastapi import APIRouter
from .auth import router as auth_router
from .xiaohongshu import router as xiaohongshu_router
from .notes import router as notes_router
from .comments import router as comments_router
from .crawler import router as crawler_router

api_router = APIRouter()

# 注册认证路由
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])

# 注册小红书账号管理路由
api_router.include_router(xiaohongshu_router, prefix="/xiaohongshu", tags=["小红书账号管理"])

# 注册笔记管理路由
api_router.include_router(notes_router, prefix="/notes", tags=["笔记管理"])

# 注册留言管理路由
api_router.include_router(comments_router, prefix="/comments", tags=["留言管理"])

# 注册爬虫管理路由
api_router.include_router(crawler_router, prefix="/crawler", tags=["爬虫管理"])

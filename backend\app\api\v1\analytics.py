from typing import Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from ...core.deps import get_current_user, get_db
from ...models.user import User
from ...services.analytics_service import AnalyticsService
from ...api.responses import success_response, error_response

router = APIRouter()


@router.get("/overview")
def get_analytics_overview(
    days: int = Query(30, ge=1, le=365, description="分析天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取分析概览"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        
        # 获取各项分析数据
        comment_analytics = analytics_service.get_comment_analytics(days)
        ai_analytics = analytics_service.get_ai_analytics(days)
        performance_metrics = analytics_service.get_performance_metrics(days)
        
        overview = {
            "period_days": days,
            "summary": {
                "total_comments": comment_analytics["total_comments"],
                "reply_rate": comment_analytics["reply_rate"],
                "ai_replies_generated": ai_analytics["total_ai_replies"],
                "efficiency_score": performance_metrics["efficiency_score"]
            },
            "trends": {
                "comment_trend": comment_analytics["daily_trend"][-7:],  # 最近7天
                "ai_usage_trend": ai_analytics["daily_trend"][-7:]
            }
        }
        
        return success_response(data=overview, message="获取分析概览成功")
    except Exception as e:
        return error_response(message=f"获取分析概览失败: {str(e)}")


@router.get("/comments")
def get_comment_analytics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取留言分析数据"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        data = analytics_service.get_comment_analytics(days)
        
        return success_response(data=data, message="获取留言分析成功")
    except Exception as e:
        return error_response(message=f"获取留言分析失败: {str(e)}")


@router.get("/ai")
def get_ai_analytics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取AI使用分析数据"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        data = analytics_service.get_ai_analytics(days)
        
        return success_response(data=data, message="获取AI分析成功")
    except Exception as e:
        return error_response(message=f"获取AI分析失败: {str(e)}")


@router.get("/templates")
def get_template_analytics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取模板使用分析"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        data = analytics_service.get_template_analytics()
        
        return success_response(data=data, message="获取模板分析成功")
    except Exception as e:
        return error_response(message=f"获取模板分析失败: {str(e)}")


@router.get("/accounts")
def get_account_analytics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取账号表现分析"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        data = analytics_service.get_account_analytics(days)
        
        return success_response(data=data, message="获取账号分析成功")
    except Exception as e:
        return error_response(message=f"获取账号分析失败: {str(e)}")


@router.get("/performance")
def get_performance_metrics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取性能指标"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        data = analytics_service.get_performance_metrics(days)
        
        return success_response(data=data, message="获取性能指标成功")
    except Exception as e:
        return error_response(message=f"获取性能指标失败: {str(e)}")


@router.get("/report")
def generate_analytics_report(
    days: int = Query(30, ge=1, le=365),
    format: str = Query("json", regex="^(json|pdf|excel)$"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """生成分析报告"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        
        if format == "json":
            data = analytics_service.generate_report(days)
            return success_response(data=data, message="生成报告成功")
        
        elif format == "pdf":
            # TODO: 实现PDF报告生成
            return error_response(message="PDF报告功能开发中")
        
        elif format == "excel":
            # TODO: 实现Excel报告生成
            return error_response(message="Excel报告功能开发中")
        
    except Exception as e:
        return error_response(message=f"生成报告失败: {str(e)}")


@router.get("/trends/comments")
def get_comment_trends(
    days: int = Query(30, ge=7, le=365),
    granularity: str = Query("daily", regex="^(hourly|daily|weekly|monthly)$"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取留言趋势数据"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        
        # 根据粒度获取不同的趋势数据
        if granularity == "daily":
            data = analytics_service.get_comment_analytics(days)
            trend_data = data["daily_trend"]
        else:
            # TODO: 实现其他粒度的趋势分析
            trend_data = []
        
        return success_response(
            data={
                "granularity": granularity,
                "period_days": days,
                "trend_data": trend_data
            },
            message="获取趋势数据成功"
        )
    except Exception as e:
        return error_response(message=f"获取趋势数据失败: {str(e)}")


@router.get("/comparison")
def get_period_comparison(
    current_days: int = Query(30, ge=1, le=365),
    previous_days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取时期对比数据"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        
        # 获取当前时期数据
        current_data = analytics_service.get_comment_analytics(current_days)
        current_ai_data = analytics_service.get_ai_analytics(current_days)
        
        # 获取对比时期数据（需要调整时间范围）
        # TODO: 实现历史时期数据获取
        previous_data = {
            "total_comments": 0,
            "reply_rate": 0
        }
        
        # 计算变化率
        comparison = {
            "current_period": {
                "days": current_days,
                "total_comments": current_data["total_comments"],
                "reply_rate": current_data["reply_rate"],
                "ai_replies": current_ai_data["total_ai_replies"]
            },
            "previous_period": {
                "days": previous_days,
                "total_comments": previous_data["total_comments"],
                "reply_rate": previous_data["reply_rate"]
            },
            "changes": {
                "comments_change": 0,  # 计算变化率
                "reply_rate_change": 0
            }
        }
        
        return success_response(data=comparison, message="获取对比数据成功")
    except Exception as e:
        return error_response(message=f"获取对比数据失败: {str(e)}")


@router.get("/export")
def export_analytics_data(
    days: int = Query(30, ge=1, le=365),
    data_type: str = Query("all", regex="^(all|comments|ai|templates|accounts)$"),
    format: str = Query("json", regex="^(json|csv|excel)$"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """导出分析数据"""
    try:
        analytics_service = AnalyticsService(db, current_user.id)
        
        # 根据数据类型获取相应数据
        if data_type == "all":
            data = analytics_service.generate_report(days)
        elif data_type == "comments":
            data = analytics_service.get_comment_analytics(days)
        elif data_type == "ai":
            data = analytics_service.get_ai_analytics(days)
        elif data_type == "templates":
            data = analytics_service.get_template_analytics()
        elif data_type == "accounts":
            data = analytics_service.get_account_analytics(days)
        
        if format == "json":
            return success_response(data=data, message="数据导出成功")
        else:
            # TODO: 实现CSV和Excel导出
            return error_response(message=f"{format.upper()}导出功能开发中")
        
    except Exception as e:
        return error_response(message=f"数据导出失败: {str(e)}")

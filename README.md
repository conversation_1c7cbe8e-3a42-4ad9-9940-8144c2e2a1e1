# 小红书自动回复工具

一个基于Python和React的小红书自动回复系统，支持自动监控笔记留言并进行智能回复。

## 🌟 功能特性

- 🔍 **自动监控**: 实时监控指定小红书笔记的新留言
- 🤖 **智能回复**: 基于AI的自动回复功能
- 📊 **数据统计**: 详细的留言和回复数据分析
- 🔐 **安全可靠**: 完善的用户认证和数据安全保护
- 🎯 **精准控制**: 灵活的回复规则和条件设置
- 👥 **多账号管理**: 支持管理多个小红书账号
- ⏰ **定时任务**: 自动化的定时抓取和回复
- 📱 **响应式界面**: 现代化的Web管理界面

## 🛠️ 技术栈

### 后端技术
- **FastAPI**: 现代化的Python Web框架，支持自动API文档
- **SQLAlchemy**: 强大的ORM框架，支持多种数据库
- **PostgreSQL**: 可靠的关系型数据库
- **Playwright**: 自动化浏览器操作和数据抓取
- **Pydantic**: 数据验证和序列化
- **JWT**: 安全的用户认证
- **APScheduler**: 定时任务调度

### 前端技术
- **React 18**: 现代化的前端框架
- **Vite**: 快速的构建工具
- **Ant Design**: 企业级UI组件库
- **TypeScript**: 类型安全的JavaScript
- **Axios**: HTTP客户端

### 开发工具
- **Docker**: 容器化部署
- **Alembic**: 数据库迁移工具
- **Pytest**: 单元测试框架
- **Black**: 代码格式化
- **ESLint**: 代码质量检查

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- Docker (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd xiaohongshu-auto-reply
```

2. **后端设置**
```bash
cd backend
pip install -r requirements.txt
```

3. **前端设置**
```bash
cd frontend
npm install
```

4. **环境配置**
```bash
# 复制环境变量模板
cp backend/.env.example backend/.env
# 编辑配置文件，填入数据库连接等信息
```

5. **数据库配置**
```bash
# 创建数据库
createdb xiaohongshu_auto_reply

# 运行迁移
cd backend
alembic upgrade head
```

6. **启动服务**
```bash
# 启动后端 (默认端口: 8000)
cd backend
uvicorn app.main:app --reload

# 启动前端 (默认端口: 3000)
cd frontend
npm run dev
```

7. **访问应用**
- 前端界面: http://localhost:3000
- 后端API文档: http://localhost:8000/docs
- 后端管理界面: http://localhost:8000/redoc

## 📁 项目结构

```
xiaohongshu-auto-reply/
├── backend/                    # 后端代码
│   ├── app/
│   │   ├── api/               # API路由层
│   │   │   ├── v1/           # API版本1
│   │   │   │   ├── auth.py   # 认证接口
│   │   │   │   ├── xiaohongshu.py # 账号管理
│   │   │   │   ├── notes.py  # 笔记管理
│   │   │   │   ├── comments.py # 留言管理
│   │   │   │   └── crawler.py # 爬虫管理
│   │   │   ├── schemas/      # 数据模型
│   │   │   ├── deps.py       # 依赖注入
│   │   │   └── responses.py  # 响应格式
│   │   ├── core/             # 核心配置
│   │   │   ├── config.py     # 应用配置
│   │   │   └── security.py   # 安全相关
│   │   ├── models/           # 数据库模型
│   │   │   ├── user.py       # 用户模型
│   │   │   ├── note.py       # 笔记模型
│   │   │   └── comment.py    # 留言模型
│   │   ├── services/         # 业务逻辑层
│   │   │   ├── user_service.py
│   │   │   ├── xiaohongshu_service.py
│   │   │   ├── note_service.py
│   │   │   ├── comment_service.py
│   │   │   └── crawler_service.py
│   │   ├── crawler/          # 爬虫工具
│   │   │   ├── xiaohongshu_analyzer.py
│   │   │   ├── login_analyzer.py
│   │   │   ├── anti_crawler_analyzer.py
│   │   │   ├── xiaohongshu_crawler.py
│   │   │   └── crawler_strategy.py
│   │   └── main.py           # 应用入口
│   ├── alembic/              # 数据库迁移
│   ├── tests/                # 测试代码
│   ├── requirements.txt      # Python依赖
│   └── .env.example          # 环境变量模板
├── frontend/                 # 前端代码
│   ├── src/
│   │   ├── components/       # 通用组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   ├── utils/           # 工具函数
│   │   ├── types/           # TypeScript类型
│   │   └── App.tsx          # 应用入口
│   ├── public/              # 静态资源
│   ├── package.json         # Node.js依赖
│   └── vite.config.ts       # Vite配置
├── docs/                    # 项目文档
│   ├── api.md              # API文档
│   ├── architecture.md     # 架构文档
│   ├── deployment.md       # 部署文档
│   └── development.md      # 开发指南
├── docker-compose.yml      # Docker编排
├── .gitignore             # Git忽略文件
├── LICENSE                # 开源许可证
└── README.md             # 项目说明
```

## 📋 API概览

### 认证接口
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/me` - 获取用户信息
- `POST /api/v1/auth/change-password` - 修改密码

### 账号管理
- `POST /api/v1/xiaohongshu/accounts` - 创建小红书账号
- `GET /api/v1/xiaohongshu/accounts` - 获取账号列表
- `PUT /api/v1/xiaohongshu/accounts/{id}` - 更新账号信息
- `DELETE /api/v1/xiaohongshu/accounts/{id}` - 删除账号

### 笔记管理
- `POST /api/v1/notes` - 添加监控笔记
- `GET /api/v1/notes` - 获取笔记列表
- `PUT /api/v1/notes/{id}` - 更新笔记配置
- `DELETE /api/v1/notes/{id}` - 删除笔记

### 留言管理
- `GET /api/v1/comments` - 获取留言列表
- `POST /api/v1/comments/{id}/reply` - 回复留言
- `POST /api/v1/comments/batch-reply` - 批量回复
- `GET /api/v1/comments/pending` - 获取待处理留言

### 爬虫管理
- `POST /api/v1/crawler/crawl` - 启动抓取任务
- `GET /api/v1/crawler/status` - 获取抓取状态
- `POST /api/v1/crawler/schedule/start` - 启动定时任务

详细API文档请查看: [API文档](docs/api.md)

## 🏗️ 开发进度

### ✅ 已完成功能
- [x] **项目初始化**: 技术选型和项目结构搭建
- [x] **小红书数据抓取研究**: 接口分析和反爬虫策略
- [x] **数据库设计**: 完整的数据模型和关系设计
- [x] **用户认证系统**: JWT认证和权限控制
- [x] **小红书账号管理**: 多账号管理和Cookie维护
- [x] **笔记管理系统**: 笔记监控和配置管理
- [x] **留言抓取服务**: 自动化留言数据抓取
- [x] **留言管理系统**: 留言处理和回复管理
- [x] **爬虫集成服务**: 定时任务和状态监控

### 🚧 进行中功能
- [ ] **前端管理界面**: React管理后台开发
- [ ] **AI智能回复**: 基于GPT的智能回复生成
- [ ] **实时通知**: WebSocket实时消息推送

### 📅 计划中功能
- [ ] **数据分析**: 留言和回复效果分析
- [ ] **回复模板**: 可配置的回复模板系统
- [ ] **用户权限**: 多级用户权限管理
- [ ] **API限流**: 接口访问频率控制
- [ ] **监控告警**: 系统状态监控和告警
- [ ] **性能优化**: 缓存和数据库优化

## 🔧 开发指南

### 代码规范
- Python代码使用Black格式化
- JavaScript/TypeScript使用ESLint和Prettier
- 提交信息遵循Conventional Commits规范
- 所有API接口需要编写测试用例

### 测试
```bash
# 运行后端测试
cd backend
pytest

# 运行前端测试
cd frontend
npm test
```

### 构建部署
```bash
# Docker部署
docker-compose up -d

# 手动部署
# 详见 docs/deployment.md
```

## 📖 文档

- [API文档](docs/api.md) - 详细的API接口说明
- [架构文档](docs/architecture.md) - 系统架构和设计思路
- [部署文档](docs/deployment.md) - 生产环境部署指南
- [开发指南](docs/development.md) - 开发环境配置和规范

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 贡献类型
- 🐛 Bug修复
- ✨ 新功能开发
- 📝 文档改进
- 🎨 UI/UX改进
- ⚡ 性能优化
- 🧪 测试用例

## ⚠️ 注意事项

- 小红书平台有严格的反爬虫机制，请合理使用，避免账号被封
- 建议设置合理的抓取频率，模拟人工操作
- 回复内容需遵守平台社区规范
- 本工具仅供学习和研究使用，请遵守相关法律法规

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 提交 [Issue](https://github.com/your-repo/issues)
- 💬 参与 [Discussions](https://github.com/your-repo/discussions)
- 📮 发送邮件至 [<EMAIL>]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

## ⭐ Star History

如果这个项目对你有帮助，请给它一个星标！

[![Star History Chart](https://api.star-history.com/svg?repos=your-username/xiaohongshu-auto-reply&type=Date)](https://star-history.com/#your-username/xiaohongshu-auto-reply&Date)

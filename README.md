# 小红书自动回复工具

一个自动化回复小红书笔记留言的工具，帮助用户提高运营效率，节省人工回复时间。

## 🚀 功能特性

- **自动获取笔记留言**: 定时拉取指定笔记下的所有新留言
- **智能自动回复**: 支持关键词匹配、正则表达式和AI智能回复
- **留言管理**: 留言展示、筛选、搜索和统计分析
- **多账号管理**: 支持多个小红书账号的管理和切换
- **回复规则配置**: 灵活的回复规则设置和模板管理

## 🛠️ 技术栈

### 后端
- **Python 3.11** + **FastAPI**
- **PostgreSQL** 数据库
- **SQLAlchemy** ORM
- **Playwright** 自动化工具
- **OpenAI GPT** AI回复

### 前端
- **React 18** + **Vite**
- **Ant Design** UI组件库
- **Zustand** 状态管理

### 部署
- **Docker** + **Docker Compose**
- **Nginx** 反向代理

## 📁 项目结构

```
xiaohongshu-auto-reply/
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── main.py         # FastAPI应用入口
│   │   ├── core/           # 核心配置
│   │   ├── api/            # API路由
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── crawler/        # 爬虫相关
│   ├── tests/              # 测试
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── pages/          # 页面
│   │   └── services/       # API服务
│   └── package.json
├── docker-compose.yml      # Docker编排
└── Documentation.md        # 详细文档
```

## 🚀 快速开始

### 环境要求

- Python 3.11+
- Node.js 18+
- PostgreSQL 15+
- Docker (可选)

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd xiaohongshu-auto-reply
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要的配置
```

3. **启动后端**
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --port 8000
```

4. **启动前端**
```bash
cd frontend
npm install
npm run dev
```

### Docker部署

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📖 API文档

启动后端服务后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## ⚠️ 注意事项

- 小红书平台有严格的反爬虫机制，请合理使用，避免账号被封
- 建议设置合理的抓取频率，模拟人工操作
- 回复内容需遵守平台社区规范

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题，请通过 Issue 联系我们。

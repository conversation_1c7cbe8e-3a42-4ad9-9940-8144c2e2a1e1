# 开发指南

本文档为开发者提供详细的开发环境配置、代码规范和最佳实践指导。

## 开发环境配置

### 前置要求

- **Python**: 3.8+ (推荐 3.11)
- **Node.js**: 16+ (推荐 18 LTS)
- **PostgreSQL**: 12+ (推荐 15)
- **Git**: 2.30+
- **IDE**: VS Code / PyCharm / WebStorm

### 环境安装

#### 1. Python环境

```bash
# 使用pyenv管理Python版本
curl https://pyenv.run | bash
pyenv install 3.11.0
pyenv global 3.11.0

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
cd backend
pip install -r requirements.txt
```

#### 2. Node.js环境

```bash
# 使用nvm管理Node.js版本
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 安装依赖
cd frontend
npm install
```

#### 3. 数据库环境

```bash
# PostgreSQL安装 (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# 创建数据库
sudo -u postgres createdb xiaohongshu_auto_reply_dev

# 创建用户
sudo -u postgres psql
CREATE USER dev_user WITH PASSWORD 'dev_password';
GRANT ALL PRIVILEGES ON DATABASE xiaohongshu_auto_reply_dev TO dev_user;
```

### IDE配置

#### VS Code配置

创建 `.vscode/settings.json`:
```json
{
  "python.defaultInterpreterPath": "./backend/venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": true,
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": ["--line-length", "88"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "eslint.workingDirectories": ["frontend"]
}
```

创建 `.vscode/extensions.json`:
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.flake8",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint"
  ]
}
```

#### PyCharm配置

1. 设置Python解释器: `File > Settings > Project > Python Interpreter`
2. 配置代码格式化: `File > Settings > Tools > External Tools`
3. 启用代码检查: `File > Settings > Editor > Inspections`

### 环境变量配置

创建 `backend/.env.dev`:
```env
# 开发环境配置
APP_NAME=小红书自动回复工具
APP_VERSION=1.0.0-dev
ENVIRONMENT=development
DEBUG=true

# 数据库配置
DATABASE_URL=postgresql://dev_user:dev_password@localhost/xiaohongshu_auto_reply_dev

# 安全配置
SECRET_KEY=dev_secret_key_change_in_production
ACCESS_TOKEN_EXPIRE_MINUTES=60

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000"]

# API配置
API_V1_STR=/api/v1

# 日志配置
LOG_LEVEL=DEBUG
```

## 项目结构详解

### 后端结构

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── api/                    # API路由层
│   │   ├── __init__.py
│   │   ├── deps.py            # 依赖注入
│   │   ├── responses.py       # 响应格式
│   │   ├── v1/                # API版本1
│   │   │   ├── __init__.py
│   │   │   ├── auth.py        # 认证路由
│   │   │   ├── xiaohongshu.py # 账号管理路由
│   │   │   ├── notes.py       # 笔记管理路由
│   │   │   ├── comments.py    # 留言管理路由
│   │   │   └── crawler.py     # 爬虫管理路由
│   │   └── schemas/           # 数据模型
│   │       ├── __init__.py
│   │       ├── auth.py        # 认证模型
│   │       ├── xiaohongshu.py # 账号模型
│   │       ├── note.py        # 笔记模型
│   │       └── comment.py     # 留言模型
│   ├── core/                  # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py          # 应用配置
│   │   └── security.py        # 安全相关
│   ├── models/                # 数据库模型
│   │   ├── __init__.py
│   │   ├── database.py        # 数据库连接
│   │   ├── user.py           # 用户模型
│   │   ├── note.py           # 笔记模型
│   │   └── comment.py        # 留言模型
│   ├── services/              # 业务逻辑层
│   │   ├── __init__.py
│   │   ├── user_service.py    # 用户服务
│   │   ├── xiaohongshu_service.py # 账号服务
│   │   ├── note_service.py    # 笔记服务
│   │   ├── comment_service.py # 留言服务
│   │   └── crawler_service.py # 爬虫服务
│   └── crawler/               # 爬虫工具
│       ├── __init__.py
│       ├── xiaohongshu_analyzer.py
│       ├── login_analyzer.py
│       ├── anti_crawler_analyzer.py
│       ├── xiaohongshu_crawler.py
│       └── crawler_strategy.py
├── alembic/                   # 数据库迁移
│   ├── versions/
│   ├── env.py
│   └── script.py.mako
├── tests/                     # 测试代码
│   ├── __init__.py
│   ├── conftest.py           # 测试配置
│   ├── test_auth.py          # 认证测试
│   ├── test_notes.py         # 笔记测试
│   └── test_comments.py      # 留言测试
├── requirements.txt           # Python依赖
├── alembic.ini               # Alembic配置
└── .env.example              # 环境变量模板
```

### 前端结构

```
frontend/
├── public/                    # 静态资源
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── components/            # 通用组件
│   │   ├── Layout/           # 布局组件
│   │   ├── Forms/            # 表单组件
│   │   ├── Tables/           # 表格组件
│   │   └── Common/           # 通用组件
│   ├── pages/                # 页面组件
│   │   ├── Auth/             # 认证页面
│   │   ├── Dashboard/        # 仪表板
│   │   ├── Accounts/         # 账号管理
│   │   ├── Notes/            # 笔记管理
│   │   └── Comments/         # 留言管理
│   ├── services/             # API服务
│   │   ├── api.ts            # API基础配置
│   │   ├── auth.ts           # 认证服务
│   │   ├── accounts.ts       # 账号服务
│   │   ├── notes.ts          # 笔记服务
│   │   └── comments.ts       # 留言服务
│   ├── stores/               # 状态管理
│   │   ├── auth.ts           # 认证状态
│   │   ├── accounts.ts       # 账号状态
│   │   └── global.ts         # 全局状态
│   ├── types/                # TypeScript类型
│   │   ├── auth.ts
│   │   ├── account.ts
│   │   ├── note.ts
│   │   └── comment.ts
│   ├── utils/                # 工具函数
│   │   ├── request.ts        # 请求工具
│   │   ├── storage.ts        # 存储工具
│   │   └── format.ts         # 格式化工具
│   ├── styles/               # 样式文件
│   │   ├── globals.css
│   │   └── components.css
│   ├── App.tsx               # 应用入口
│   └── main.tsx              # 主入口
├── package.json              # Node.js依赖
├── tsconfig.json             # TypeScript配置
├── vite.config.ts            # Vite配置
└── .eslintrc.js              # ESLint配置
```

## 开发流程

### 1. 功能开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发功能
# - 编写代码
# - 编写测试
# - 更新文档

# 3. 运行测试
cd backend && pytest
cd frontend && npm test

# 4. 代码格式化
cd backend && black . && isort .
cd frontend && npm run lint:fix

# 5. 提交代码
git add .
git commit -m "feat: add new feature"

# 6. 推送分支
git push origin feature/new-feature

# 7. 创建Pull Request
```

### 2. 数据库迁移流程

```bash
# 1. 修改模型
# 编辑 app/models/*.py

# 2. 生成迁移文件
alembic revision --autogenerate -m "add new table"

# 3. 检查迁移文件
# 编辑 alembic/versions/*.py

# 4. 应用迁移
alembic upgrade head

# 5. 测试迁移
pytest tests/test_models.py
```

### 3. API开发流程

```bash
# 1. 定义数据模型
# 编辑 app/api/schemas/*.py

# 2. 实现业务逻辑
# 编辑 app/services/*.py

# 3. 创建API路由
# 编辑 app/api/v1/*.py

# 4. 编写测试
# 编辑 tests/test_*.py

# 5. 更新API文档
# 编辑 docs/api.md
```

## 代码规范

### Python代码规范

#### 1. 代码格式化

使用 Black 进行代码格式化：
```bash
black --line-length 88 .
```

#### 2. 导入排序

使用 isort 进行导入排序：
```bash
isort .
```

#### 3. 代码检查

使用 flake8 进行代码检查：
```bash
flake8 --max-line-length 88 --extend-ignore E203,W503 .
```

#### 4. 类型提示

```python
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

def get_users(
    db: Session, 
    skip: int = 0, 
    limit: int = 100
) -> List[User]:
    """获取用户列表"""
    return db.query(User).offset(skip).limit(limit).all()

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    is_active: bool
```

#### 5. 文档字符串

```python
def create_user(db: Session, user_data: UserCreate) -> User:
    """
    创建新用户
    
    Args:
        db: 数据库会话
        user_data: 用户创建数据
        
    Returns:
        User: 创建的用户对象
        
    Raises:
        HTTPException: 当用户名已存在时
    """
    # 实现代码...
```

### JavaScript/TypeScript代码规范

#### 1. ESLint配置

```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended'
  ],
  rules: {
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    '@typescript-eslint/no-unused-vars': 'error',
    'react/prop-types': 'off'
  }
};
```

#### 2. Prettier配置

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

#### 3. 组件规范

```typescript
// 函数组件
interface UserListProps {
  users: User[];
  onUserSelect: (user: User) => void;
}

const UserList: React.FC<UserListProps> = ({ users, onUserSelect }) => {
  return (
    <div className="user-list">
      {users.map(user => (
        <UserCard 
          key={user.id} 
          user={user} 
          onClick={() => onUserSelect(user)} 
        />
      ))}
    </div>
  );
};

export default UserList;
```

#### 4. API服务规范

```typescript
// services/api.ts
import axios from 'axios';

const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 10000,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      // 处理认证失败
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
```

## 测试指南

### 后端测试

#### 1. 单元测试

```python
# tests/test_user_service.py
import pytest
from app.services.user_service import UserService
from app.api.schemas.auth import UserCreate

def test_create_user(db_session):
    """测试创建用户"""
    user_service = UserService(db_session)
    user_data = UserCreate(
        username="testuser",
        email="<EMAIL>",
        password="password123"
    )
    
    user = user_service.create_user(user_data)
    
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert user.is_active is True
```

#### 2. API测试

```python
# tests/test_auth_api.py
def test_register_user(client):
    """测试用户注册API"""
    response = client.post(
        "/api/v1/auth/register",
        json={
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "access_token" in data["data"]
```

#### 3. 测试配置

```python
# tests/conftest.py
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.models.database import Base, get_db

SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture
def db_session():
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client(db_session):
    def override_get_db():
        try:
            yield db_session
        finally:
            db_session.close()
    
    app.dependency_overrides[get_db] = override_get_db
    yield TestClient(app)
    app.dependency_overrides.clear()
```

### 前端测试

#### 1. 组件测试

```typescript
// __tests__/UserList.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import UserList from '../components/UserList';

const mockUsers = [
  { id: 1, username: 'user1', email: '<EMAIL>' },
  { id: 2, username: 'user2', email: '<EMAIL>' },
];

test('renders user list', () => {
  const onUserSelect = jest.fn();
  
  render(<UserList users={mockUsers} onUserSelect={onUserSelect} />);
  
  expect(screen.getByText('user1')).toBeInTheDocument();
  expect(screen.getByText('user2')).toBeInTheDocument();
});

test('calls onUserSelect when user is clicked', () => {
  const onUserSelect = jest.fn();
  
  render(<UserList users={mockUsers} onUserSelect={onUserSelect} />);
  
  fireEvent.click(screen.getByText('user1'));
  expect(onUserSelect).toHaveBeenCalledWith(mockUsers[0]);
});
```

#### 2. API测试

```typescript
// __tests__/api.test.ts
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { authService } from '../services/auth';

const server = setupServer(
  rest.post('/api/v1/auth/login', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: {
          access_token: 'mock-token',
          user: { id: 1, username: 'testuser' }
        }
      })
    );
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

test('login returns token', async () => {
  const result = await authService.login('testuser', 'password');
  
  expect(result.success).toBe(true);
  expect(result.data.access_token).toBe('mock-token');
});
```

## 调试指南

### 后端调试

#### 1. 日志调试

```python
import logging
from loguru import logger

# 配置日志
logger.add("debug.log", level="DEBUG")

# 使用日志
logger.debug("Debug message")
logger.info("Info message")
logger.warning("Warning message")
logger.error("Error message")
```

#### 2. 断点调试

```python
# 使用pdb
import pdb; pdb.set_trace()

# 使用ipdb (推荐)
import ipdb; ipdb.set_trace()
```

#### 3. 性能分析

```python
import cProfile
import pstats

# 性能分析
profiler = cProfile.Profile()
profiler.enable()

# 执行代码
your_function()

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats()
```

### 前端调试

#### 1. 浏览器调试

```typescript
// 控制台调试
console.log('Debug info:', data);
console.error('Error:', error);
console.table(users);

// 断点调试
debugger;
```

#### 2. React DevTools

安装React DevTools浏览器扩展，用于调试React组件状态和props。

#### 3. 网络调试

使用浏览器开发者工具的Network面板监控API请求。

## 最佳实践

### 1. 代码组织

- 保持函数简短，单一职责
- 使用有意义的变量和函数名
- 避免深层嵌套，使用早期返回
- 合理使用注释和文档字符串

### 2. 错误处理

```python
# 后端错误处理
try:
    result = risky_operation()
    return success_response(data=result)
except SpecificException as e:
    logger.error(f"Specific error: {e}")
    raise HTTPException(status_code=400, detail="Specific error message")
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise HTTPException(status_code=500, detail="Internal server error")
```

```typescript
// 前端错误处理
try {
  const data = await api.get('/users');
  setUsers(data);
} catch (error) {
  console.error('Failed to fetch users:', error);
  message.error('获取用户列表失败');
}
```

### 3. 性能优化

- 使用数据库索引
- 实现查询缓存
- 使用分页查询
- 优化前端渲染
- 使用懒加载

### 4. 安全考虑

- 输入验证和清理
- SQL注入防护
- XSS攻击防护
- CSRF保护
- 敏感信息加密

## 常见问题

### 1. 数据库连接问题

```bash
# 检查数据库状态
sudo systemctl status postgresql

# 测试连接
psql -h localhost -U username -d database_name
```

### 2. 依赖冲突

```bash
# Python依赖冲突
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall

# Node.js依赖冲突
rm -rf node_modules package-lock.json
npm install
```

### 3. 端口占用

```bash
# 查找占用端口的进程
lsof -i :8000
netstat -tulpn | grep :8000

# 杀死进程
kill -9 <PID>
```

### 4. 权限问题

```bash
# 文件权限
chmod +x script.sh
chown user:group file

# 数据库权限
GRANT ALL PRIVILEGES ON DATABASE dbname TO username;
```

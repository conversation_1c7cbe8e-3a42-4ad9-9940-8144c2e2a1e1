import React, { useState, useEffect, useRef } from 'react';
import {
  Badge,
  Dropdown,
  List,
  Button,
  Typography,
  Space,
  Tag,
  Empty,
  Spin,
  message,
  Avatar,
} from 'antd';
import {
  BellOutlined,
  MessageOutlined,
  RobotOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseOutlined,
  SettingOutlined,
  ClearOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '../../stores/authStore';

const { Text, Title } = Typography;

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [connected, setConnected] = useState(false);
  const [visible, setVisible] = useState(false);
  const wsRef = useRef(null);
  const { user } = useAuthStore();

  useEffect(() => {
    if (user) {
      connectWebSocket();
      loadNotifications();
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [user]);

  const connectWebSocket = () => {
    if (!user?.token) return;

    const wsUrl = `ws://localhost:8000/api/v1/ws?token=${user.token}`;
    
    try {
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        setConnected(true);
        console.log('WebSocket连接已建立');
        
        // 发送心跳
        const heartbeat = setInterval(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
              type: 'ping',
              timestamp: Date.now()
            }));
          } else {
            clearInterval(heartbeat);
          }
        }, 30000);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };

      wsRef.current.onclose = () => {
        setConnected(false);
        console.log('WebSocket连接已断开');
        
        // 尝试重连
        setTimeout(() => {
          if (user?.token) {
            connectWebSocket();
          }
        }, 5000);
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket错误:', error);
        setConnected(false);
      };

    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }
  };

  const handleWebSocketMessage = (data) => {
    const { type, title, message: msg, priority, timestamp } = data;

    // 创建通知对象
    const notification = {
      id: Date.now() + Math.random(),
      type,
      title: title || '系统通知',
      message: msg || '',
      priority: priority || 'normal',
      timestamp: timestamp || new Date().toISOString(),
      read: false,
      data: data.data || {}
    };

    // 添加到通知列表
    setNotifications(prev => [notification, ...prev.slice(0, 49)]); // 保持最多50条
    setUnreadCount(prev => prev + 1);

    // 显示浏览器通知
    if (priority === 'high' || type === 'error') {
      showBrowserNotification(notification);
    }

    // 显示消息提示
    if (type === 'error') {
      message.error(notification.message);
    } else if (type === 'new_comment') {
      message.info(`收到新留言: ${notification.message}`);
    } else if (type === 'ai_reply_generated') {
      message.success('AI回复已生成');
    }
  };

  const showBrowserNotification = (notification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.type
      });
    }
  };

  const loadNotifications = async () => {
    setLoading(true);
    try {
      // 模拟加载历史通知
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockNotifications = [
        {
          id: 1,
          type: 'new_comment',
          title: '收到新留言',
          message: '用户小红在笔记中留言',
          priority: 'normal',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          read: false
        },
        {
          id: 2,
          type: 'ai_reply_generated',
          title: 'AI回复已生成',
          message: 'AI已为留言生成回复建议',
          priority: 'normal',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          read: true
        }
      ];
      
      setNotifications(mockNotifications);
      setUnreadCount(mockNotifications.filter(n => !n.read).length);
    } catch (error) {
      console.error('加载通知失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    setUnreadCount(0);
  };

  const clearAll = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  const deleteNotification = (notificationId) => {
    const notification = notifications.find(n => n.id === notificationId);
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    if (notification && !notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'new_comment':
        return <MessageOutlined style={{ color: '#1890ff' }} />;
      case 'ai_reply_generated':
        return <RobotOutlined style={{ color: '#722ed1' }} />;
      case 'error':
        return <WarningOutlined style={{ color: '#f5222d' }} />;
      case 'reply_success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      default:
        return <BellOutlined style={{ color: '#666' }} />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'red';
      case 'normal':
        return 'blue';
      case 'low':
        return 'green';
      default:
        return 'default';
    }
  };

  const formatTime = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    return time.toLocaleDateString();
  };

  const requestNotificationPermission = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  useEffect(() => {
    requestNotificationPermission();
  }, []);

  const notificationList = (
    <div style={{ width: 400, maxHeight: 500, overflow: 'auto' }}>
      <div style={{ padding: '12px 16px', borderBottom: '1px solid #f0f0f0' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={5} style={{ margin: 0 }}>
            通知中心
            {!connected && (
              <Tag color="orange" style={{ marginLeft: 8 }}>
                连接中断
              </Tag>
            )}
          </Title>
          <Space>
            <Button 
              type="link" 
              size="small" 
              onClick={markAllAsRead}
              disabled={unreadCount === 0}
            >
              全部已读
            </Button>
            <Button 
              type="link" 
              size="small" 
              icon={<ClearOutlined />}
              onClick={clearAll}
              disabled={notifications.length === 0}
            >
              清空
            </Button>
          </Space>
        </div>
      </div>

      <Spin spinning={loading}>
        {notifications.length === 0 ? (
          <div style={{ padding: 40 }}>
            <Empty 
              description="暂无通知" 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          <List
            dataSource={notifications}
            renderItem={(item) => (
              <List.Item
                style={{
                  padding: '12px 16px',
                  backgroundColor: item.read ? 'transparent' : '#f6ffed',
                  cursor: 'pointer'
                }}
                onClick={() => !item.read && markAsRead(item.id)}
                actions={[
                  <Button
                    type="link"
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteNotification(item.id);
                    }}
                  />
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      icon={getNotificationIcon(item.type)}
                      style={{ backgroundColor: 'transparent' }}
                    />
                  }
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text strong={!item.read}>{item.title}</Text>
                      <Space>
                        <Tag color={getPriorityColor(item.priority)} size="small">
                          {item.priority}
                        </Tag>
                        {!item.read && (
                          <div style={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: '#1890ff'
                          }} />
                        )}
                      </Space>
                    </div>
                  }
                  description={
                    <div>
                      <Text type="secondary">{item.message}</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {formatTime(item.timestamp)}
                      </Text>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </Spin>

      <div style={{ padding: '8px 16px', borderTop: '1px solid #f0f0f0', textAlign: 'center' }}>
        <Button type="link" icon={<SettingOutlined />} size="small">
          通知设置
        </Button>
      </div>
    </div>
  );

  return (
    <Dropdown
      overlay={notificationList}
      trigger={['click']}
      placement="bottomRight"
      visible={visible}
      onVisibleChange={setVisible}
    >
      <Badge count={unreadCount} size="small">
        <Button
          type="text"
          icon={<BellOutlined />}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 40,
            height: 40
          }}
        />
      </Badge>
    </Dropdown>
  );
};

export default NotificationCenter;

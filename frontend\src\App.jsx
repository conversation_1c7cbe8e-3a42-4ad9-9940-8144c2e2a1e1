import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useAuthStore } from './stores/authStore';

// 组件导入
import MainLayout from './components/Layout/MainLayout';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import LoginPage from './pages/Auth/LoginPage';
import DashboardPage from './pages/Dashboard/DashboardPage';
import AccountsPage from './pages/Accounts/AccountsPage';
import NotesPage from './pages/Notes/NotesPage';
import CommentsPage from './pages/Comments/CommentsPage';
import CrawlerPage from './pages/Crawler/CrawlerPage';
import SettingsPage from './pages/Settings/SettingsPage';

// 样式导入
import './App.css';

function App() {
  const { initAuth } = useAuthStore();

  useEffect(() => {
    // 应用启动时初始化认证状态
    initAuth();
  }, [initAuth]);

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <AntdApp>
        <Router>
          <div className="app">
            <Routes>
              {/* 登录页面 */}
              <Route path="/login" element={<LoginPage />} />

              {/* 受保护的路由 */}
              <Route path="/*" element={
                <ProtectedRoute>
                  <MainLayout>
                    <Routes>
                      {/* 仪表盘 */}
                      <Route path="/" element={<DashboardPage />} />

                      {/* 账号管理 */}
                      <Route path="/accounts" element={<AccountsPage />} />

                      {/* 笔记管理 */}
                      <Route path="/notes" element={<NotesPage />} />

                      {/* 留言管理 */}
                      <Route path="/comments" element={<CommentsPage />} />

                      {/* 爬虫管理 */}
                      <Route path="/crawler" element={<CrawlerPage />} />

                      {/* 系统设置 */}
                      <Route path="/settings" element={<SettingsPage />} />

                      {/* 个人资料 */}
                      <Route path="/profile" element={<div>个人资料页面开发中...</div>} />

                      {/* 404页面 */}
                      <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                  </MainLayout>
                </ProtectedRoute>
              } />
            </Routes>
          </div>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;

"""
笔记相关的Pydantic模型
"""
from typing import Optional
from pydantic import BaseModel, validator, HttpUrl
from datetime import datetime
from enum import Enum


class NoteStatusEnum(str, Enum):
    """笔记状态枚举"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


class NoteBase(BaseModel):
    """笔记基础模型"""
    note_url: str
    title: Optional[str] = None
    crawl_interval: int = 300  # 抓取间隔（秒），默认5分钟
    auto_reply_enabled: bool = True
    
    @validator('note_url')
    def validate_note_url(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('笔记URL不能为空')
        
        # 简单的小红书URL验证
        if 'xiaohongshu.com' not in v and 'xhslink.com' not in v:
            raise ValueError('请输入有效的小红书笔记链接')
        
        return v.strip()
    
    @validator('crawl_interval')
    def validate_crawl_interval(cls, v):
        if v < 60:  # 最小1分钟
            raise ValueError('抓取间隔不能少于60秒')
        if v > 86400:  # 最大24小时
            raise ValueError('抓取间隔不能超过24小时')
        return v


class NoteCreate(NoteBase):
    """创建笔记模型"""
    account_id: int
    
    @validator('account_id')
    def validate_account_id(cls, v):
        if v <= 0:
            raise ValueError('账号ID必须大于0')
        return v


class NoteUpdate(BaseModel):
    """更新笔记模型"""
    title: Optional[str] = None
    crawl_interval: Optional[int] = None
    auto_reply_enabled: Optional[bool] = None
    status: Optional[NoteStatusEnum] = None
    
    @validator('crawl_interval')
    def validate_crawl_interval(cls, v):
        if v is not None:
            if v < 60:
                raise ValueError('抓取间隔不能少于60秒')
            if v > 86400:
                raise ValueError('抓取间隔不能超过24小时')
        return v


class NoteResponse(NoteBase):
    """笔记响应模型"""
    id: int
    account_id: int
    note_id: Optional[str] = None
    author_name: Optional[str] = None
    author_id: Optional[str] = None
    content: Optional[str] = None
    publish_time: Optional[datetime] = None
    likes_count: int = 0
    comments_count: int = 0
    shares_count: int = 0
    status: NoteStatusEnum
    last_crawled: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class NoteStats(BaseModel):
    """笔记统计模型"""
    note_id: int
    total_comments: int = 0
    new_comments: int = 0
    replied_comments: int = 0
    pending_comments: int = 0
    last_activity: Optional[datetime] = None


class NoteBatchOperation(BaseModel):
    """笔记批量操作模型"""
    note_ids: list[int]
    operation: str  # activate, deactivate, delete
    
    @validator('note_ids')
    def validate_note_ids(cls, v):
        if not v or len(v) == 0:
            raise ValueError('笔记ID列表不能为空')
        if len(v) > 50:  # 限制批量操作数量
            raise ValueError('批量操作最多支持50个笔记')
        return v
    
    @validator('operation')
    def validate_operation(cls, v):
        allowed_operations = ['activate', 'deactivate', 'delete']
        if v not in allowed_operations:
            raise ValueError(f'操作类型必须是: {", ".join(allowed_operations)}')
        return v


class NoteSearchFilter(BaseModel):
    """笔记搜索过滤器"""
    account_id: Optional[int] = None
    status: Optional[NoteStatusEnum] = None
    auto_reply_enabled: Optional[bool] = None
    keyword: Optional[str] = None  # 搜索标题或内容
    
    @validator('keyword')
    def validate_keyword(cls, v):
        if v is not None and len(v.strip()) == 0:
            return None
        return v.strip() if v else None

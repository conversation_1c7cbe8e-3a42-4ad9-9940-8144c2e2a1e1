"""
爬虫管理API路由
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from ...models import get_db, User
from ...services.crawler_service import CrawlerService
from ...api.schemas.comment import CrawlRequest
from ...api.responses import success_response
from ...api.deps import get_current_active_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/crawl", response_model=dict)
async def start_crawl(
    crawl_request: CrawlRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """启动爬虫抓取"""
    try:
        crawler_service = CrawlerService(db)
        
        # 如果指定了笔记ID，验证权限
        if crawl_request.note_ids:
            from ...services.note_service import NoteService
            note_service = NoteService(db)
            
            for note_id in crawl_request.note_ids:
                note = note_service.get_note_by_id(note_id, current_user.id)
                if not note:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"笔记 {note_id} 不存在或无权限访问"
                    )
        
        # 启动后台抓取任务
        background_tasks.add_task(
            _background_crawl_task,
            crawler_service,
            crawl_request.note_ids,
            crawl_request.force_crawl
        )
        
        return success_response(
            data={
                "message": "抓取任务已启动",
                "note_ids": crawl_request.note_ids,
                "force_crawl": crawl_request.force_crawl
            },
            message="爬虫抓取任务启动成功"
        )
        
    except HTTPException as e:
        logger.warning(f"启动爬虫抓取失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"启动爬虫抓取时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动爬虫抓取失败，请稍后重试"
        )


@router.get("/status", response_model=dict)
def get_crawl_status(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取爬虫状态"""
    try:
        crawler_service = CrawlerService(db)
        status_data = crawler_service.get_crawl_status(current_user.id)
        
        return success_response(
            data=status_data,
            message="获取爬虫状态成功"
        )
        
    except Exception as e:
        logger.error(f"获取爬虫状态时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取爬虫状态失败"
        )


@router.post("/test", response_model=dict)
async def test_crawl(
    note_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """测试单个笔记的爬虫抓取"""
    try:
        from ...services.note_service import NoteService
        
        # 验证笔记权限
        note_service = NoteService(db)
        note = note_service.get_note_by_id(note_id, current_user.id)
        
        if not note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="笔记不存在或无权限访问"
            )
        
        # 执行测试抓取
        crawler_service = CrawlerService(db)
        results = await crawler_service.crawl_notes([note_id], force_crawl=True)
        
        if results:
            result = results[0]
            return success_response(
                data={
                    "note_id": result.note_id,
                    "success": result.success,
                    "new_comments_count": result.new_comments_count,
                    "error_message": result.error_message,
                    "crawl_time": result.crawl_time.isoformat()
                },
                message="测试抓取完成"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="测试抓取失败"
            )
        
    except HTTPException as e:
        logger.warning(f"测试爬虫抓取失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"测试爬虫抓取时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="测试爬虫抓取失败，请稍后重试"
        )


@router.get("/logs", response_model=dict)
def get_crawl_logs(
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取爬虫日志"""
    try:
        # 这里可以实现获取爬虫日志的逻辑
        # 目前返回模拟数据
        
        logs = [
            {
                "timestamp": "2024-01-01T10:00:00",
                "level": "INFO",
                "message": "开始抓取笔记: 测试笔记1",
                "note_id": 1
            },
            {
                "timestamp": "2024-01-01T10:01:00",
                "level": "SUCCESS",
                "message": "抓取完成，新增留言 5 条",
                "note_id": 1
            }
        ]
        
        return success_response(
            data=logs,
            message="获取爬虫日志成功"
        )
        
    except Exception as e:
        logger.error(f"获取爬虫日志时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取爬虫日志失败"
        )


@router.post("/schedule/start", response_model=dict)
def start_scheduled_crawling(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """启动定时爬虫任务"""
    try:
        # 这里可以实现启动定时任务的逻辑
        # 例如使用APScheduler或Celery
        
        return success_response(
            data={"status": "started"},
            message="定时爬虫任务启动成功"
        )
        
    except Exception as e:
        logger.error(f"启动定时爬虫任务时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动定时爬虫任务失败"
        )


@router.post("/schedule/stop", response_model=dict)
def stop_scheduled_crawling(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """停止定时爬虫任务"""
    try:
        # 这里可以实现停止定时任务的逻辑
        
        return success_response(
            data={"status": "stopped"},
            message="定时爬虫任务停止成功"
        )
        
    except Exception as e:
        logger.error(f"停止定时爬虫任务时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="停止定时爬虫任务失败"
        )


@router.get("/schedule/status", response_model=dict)
def get_schedule_status(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取定时任务状态"""
    try:
        # 这里可以实现获取定时任务状态的逻辑
        
        status_data = {
            "is_running": False,
            "next_run_time": None,
            "last_run_time": None,
            "total_runs": 0,
            "success_runs": 0,
            "failed_runs": 0
        }
        
        return success_response(
            data=status_data,
            message="获取定时任务状态成功"
        )
        
    except Exception as e:
        logger.error(f"获取定时任务状态时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取定时任务状态失败"
        )


async def _background_crawl_task(
    crawler_service: CrawlerService,
    note_ids: List[int] = None,
    force_crawl: bool = False
):
    """后台爬虫抓取任务"""
    try:
        logger.info(f"开始后台抓取任务: note_ids={note_ids}, force_crawl={force_crawl}")
        
        results = await crawler_service.crawl_notes(note_ids, force_crawl)
        
        success_count = sum(1 for r in results if r.success)
        total_count = len(results)
        
        logger.info(f"后台抓取任务完成: 成功 {success_count}/{total_count}")
        
    except Exception as e:
        logger.error(f"后台抓取任务失败: {e}")

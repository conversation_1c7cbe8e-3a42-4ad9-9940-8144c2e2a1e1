"""
回复规则相关数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Enum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .database import Base
import enum


class RuleType(enum.Enum):
    """规则类型枚举"""
    KEYWORD = "keyword"  # 关键词匹配
    REGEX = "regex"  # 正则表达式
    AI = "ai"  # AI智能回复
    TEMPLATE = "template"  # 模板回复


class ReplyRule(Base):
    """回复规则表"""
    __tablename__ = "reply_rules"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String(100), nullable=False)  # 规则名称
    description = Column(Text)  # 规则描述
    rule_type = Column(Enum(RuleType), nullable=False)  # 规则类型
    trigger_condition = Column(Text, nullable=False)  # 触发条件
    reply_templates = Column(JSON)  # 回复模板列表
    priority = Column(Integer, default=0)  # 优先级（数字越大优先级越高）
    is_active = Column(Boolean, default=True)  # 是否启用
    match_count = Column(Integer, default=0)  # 匹配次数
    success_count = Column(Integer, default=0)  # 成功回复次数
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="reply_rules")
    
    def __repr__(self):
        return f"<ReplyRule(id={self.id}, name='{self.name}', type='{self.rule_type}')>"


class ReplyLog(Base):
    """回复日志表"""
    __tablename__ = "reply_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    comment_id = Column(Integer, ForeignKey("comments.id"), nullable=False)
    rule_id = Column(Integer, ForeignKey("reply_rules.id"))
    original_comment = Column(Text, nullable=False)  # 原始评论内容
    reply_content = Column(Text, nullable=False)  # 回复内容
    reply_method = Column(String(20), nullable=False)  # 回复方式：auto, manual
    status = Column(String(20), default="pending")  # 状态：pending, success, failed
    error_message = Column(Text)  # 错误信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<ReplyLog(id={self.id}, status='{self.status}')>"


class SystemLog(Base):
    """系统日志表"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    level = Column(String(20), nullable=False)  # 日志级别：INFO, WARNING, ERROR
    module = Column(String(50))  # 模块名称
    action = Column(String(100))  # 操作名称
    message = Column(Text, nullable=False)  # 日志消息
    details = Column(JSON)  # 详细信息
    user_id = Column(Integer, ForeignKey("users.id"))  # 相关用户ID
    ip_address = Column(String(45))  # IP地址
    user_agent = Column(String(500))  # 用户代理
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<SystemLog(id={self.id}, level='{self.level}', action='{self.action}')>"

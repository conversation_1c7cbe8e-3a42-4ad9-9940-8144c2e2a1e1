# 部署文档

本文档详细说明了小红书自动回复工具的部署方法和配置。

## 部署方式

### 1. Docker部署 (推荐)

Docker部署是最简单和可靠的部署方式，适合生产环境。

#### 前置要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB内存
- 至少10GB磁盘空间

#### 部署步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd xia<PERSON><PERSON>shu-auto-reply
```

2. **配置环境变量**
```bash
cp backend/.env.example backend/.env
```

编辑 `backend/.env` 文件：
```env
# 应用配置
APP_NAME=小红书自动回复工具
APP_VERSION=1.0.0
ENVIRONMENT=production
DEBUG=false

# 数据库配置
DATABASE_URL=**************************************/xiaohongshu_auto_reply
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=xia<PERSON><PERSON><PERSON>_auto_reply

# 安全配置
SECRET_KEY=your_very_secure_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000","https://yourdomain.com"]

# API配置
API_V1_STR=/api/v1

# 日志配置
LOG_LEVEL=INFO
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **初始化数据库**
```bash
# 等待数据库启动
sleep 30

# 运行数据库迁移
docker-compose exec backend alembic upgrade head
```

5. **验证部署**
```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 测试API
curl http://localhost:8000/docs
```

#### 服务访问地址
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs
- 数据库: localhost:5432

### 2. 手动部署

适合开发环境或需要自定义配置的场景。

#### 前置要求
- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- Redis 6+ (可选)

#### 后端部署

1. **安装Python依赖**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **配置数据库**
```bash
# 创建数据库
createdb xiaohongshu_auto_reply

# 设置环境变量
export DATABASE_URL="postgresql://username:password@localhost/xiaohongshu_auto_reply"
export SECRET_KEY="your_secret_key"
```

3. **运行数据库迁移**
```bash
alembic upgrade head
```

4. **启动后端服务**
```bash
# 开发环境
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 生产环境
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

#### 前端部署

1. **安装Node.js依赖**
```bash
cd frontend
npm install
```

2. **构建生产版本**
```bash
npm run build
```

3. **部署静态文件**
```bash
# 使用Nginx
sudo cp -r dist/* /var/www/html/

# 或使用Node.js服务器
npm run preview
```

### 3. 云平台部署

#### AWS部署

1. **使用ECS + RDS**
```bash
# 构建Docker镜像
docker build -t xiaohongshu-backend ./backend
docker build -t xiaohongshu-frontend ./frontend

# 推送到ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin <account>.dkr.ecr.us-west-2.amazonaws.com
docker tag xiaohongshu-backend:latest <account>.dkr.ecr.us-west-2.amazonaws.com/xiaohongshu-backend:latest
docker push <account>.dkr.ecr.us-west-2.amazonaws.com/xiaohongshu-backend:latest
```

2. **配置ECS任务定义**
```json
{
  "family": "xiaohongshu-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "<account>.dkr.ecr.us-west-2.amazonaws.com/xiaohongshu-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DATABASE_URL",
          "value": "************************************************/xiaohongshu"
        }
      ]
    }
  ]
}
```

#### 阿里云部署

1. **使用容器服务ACK**
```yaml
# kubernetes-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: xiaohongshu-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: xiaohongshu-backend
  template:
    metadata:
      labels:
        app: xiaohongshu-backend
    spec:
      containers:
      - name: backend
        image: registry.cn-hangzhou.aliyuncs.com/your-namespace/xiaohongshu-backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
```

## 配置说明

### 环境变量配置

#### 必需配置
```env
# 数据库连接
DATABASE_URL=postgresql://user:pass@host:port/dbname

# 安全密钥
SECRET_KEY=your_secret_key_at_least_32_characters

# 应用环境
ENVIRONMENT=production
DEBUG=false
```

#### 可选配置
```env
# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/xiaohongshu/app.log

# 缓存配置
REDIS_URL=redis://localhost:6379/0

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# 第三方服务
OPENAI_API_KEY=your_openai_api_key
```

### Nginx配置

```nginx
# /etc/nginx/sites-available/xiaohongshu
server {
    listen 80;
    server_name yourdomain.com;
    
    # 前端静态文件
    location / {
        root /var/www/xiaohongshu/frontend;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket支持
    location /ws/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

# HTTPS配置
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 其他配置同上...
}
```

### 数据库配置

#### PostgreSQL优化
```sql
-- postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

#### 数据库备份
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/postgresql"
DB_NAME="xiaohongshu_auto_reply"

# 创建备份
pg_dump $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete
```

## 监控和日志

### 日志配置

#### 应用日志
```python
# backend/app/core/logging.py
import logging
from loguru import logger

# 配置日志格式
logger.add(
    "/var/log/xiaohongshu/app.log",
    rotation="1 day",
    retention="30 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)
```

#### Nginx日志
```nginx
# 访问日志格式
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for"';

access_log /var/log/nginx/xiaohongshu_access.log main;
error_log /var/log/nginx/xiaohongshu_error.log;
```

### 监控配置

#### Prometheus监控
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'xiaohongshu-backend'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

#### 健康检查
```python
# backend/app/api/v1/health.py
@router.get("/health")
def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": settings.APP_VERSION
    }
```

## 安全配置

### SSL/TLS配置

```bash
# 使用Let's Encrypt
sudo certbot --nginx -d yourdomain.com

# 手动证书配置
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/xiaohongshu.key \
    -out /etc/ssl/certs/xiaohongshu.crt
```

### 防火墙配置

```bash
# UFW配置
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# iptables配置
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
sudo iptables -A INPUT -j DROP
```

### 数据库安全

```sql
-- 创建专用数据库用户
CREATE USER xiaohongshu_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE xiaohongshu_auto_reply TO xiaohongshu_user;
GRANT USAGE ON SCHEMA public TO xiaohongshu_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO xiaohongshu_user;
```

## 性能优化

### 应用优化

```python
# 连接池配置
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)
```

### 缓存配置

```python
# Redis缓存
import redis

redis_client = redis.Redis(
    host='localhost',
    port=6379,
    db=0,
    decode_responses=True,
    max_connections=20
)
```

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库状态
sudo systemctl status postgresql

# 检查连接
psql -h localhost -U postgres -d xiaohongshu_auto_reply

# 查看日志
sudo tail -f /var/log/postgresql/postgresql-*.log
```

#### 2. 应用启动失败
```bash
# 检查Python环境
python --version
pip list

# 检查依赖
pip install -r requirements.txt

# 查看详细错误
python -m app.main
```

#### 3. 前端构建失败
```bash
# 清理缓存
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# 检查Node版本
node --version
npm --version
```

### 日志分析

```bash
# 查看应用日志
tail -f /var/log/xiaohongshu/app.log

# 查看Nginx日志
tail -f /var/log/nginx/xiaohongshu_error.log

# 查看系统日志
journalctl -u xiaohongshu-backend -f
```

## 维护和更新

### 应用更新

```bash
# 1. 备份数据
pg_dump xiaohongshu_auto_reply > backup_$(date +%Y%m%d).sql

# 2. 拉取最新代码
git pull origin main

# 3. 更新依赖
pip install -r requirements.txt
npm install

# 4. 运行迁移
alembic upgrade head

# 5. 重启服务
docker-compose restart
```

### 定期维护

```bash
#!/bin/bash
# maintenance.sh

# 清理日志
find /var/log/xiaohongshu -name "*.log" -mtime +30 -delete

# 清理临时文件
find /tmp -name "xiaohongshu_*" -mtime +1 -delete

# 数据库维护
psql -d xiaohongshu_auto_reply -c "VACUUM ANALYZE;"

# 重启服务
docker-compose restart
```

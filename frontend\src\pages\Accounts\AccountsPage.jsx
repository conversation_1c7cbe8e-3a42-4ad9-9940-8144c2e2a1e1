import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
} from '@ant-design/icons';

const { Title } = Typography;

const AccountsPage = () => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setAccounts([
        {
          id: 1,
          account_name: '主账号',
          account_id: 'xiaohongshu_user_001',
          login_phone: '138****8888',
          login_email: '<EMAIL>',
          is_active: true,
          last_login: '2024-01-18 10:30:00',
          created_at: '2024-01-15 09:00:00',
        },
        {
          id: 2,
          account_name: '备用账号',
          account_id: 'xiaohongshu_user_002',
          login_phone: '139****9999',
          login_email: '<EMAIL>',
          is_active: false,
          last_login: '2024-01-17 15:20:00',
          created_at: '2024-01-16 14:30:00',
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  // 表格列配置
  const columns = [
    {
      title: '账号名称',
      dataIndex: 'account_name',
      key: 'account_name',
      render: (text) => <strong>{text}</strong>,
    },
    {
      title: '小红书ID',
      dataIndex: 'account_id',
      key: 'account_id',
    },
    {
      title: '登录手机',
      dataIndex: 'login_phone',
      key: 'login_phone',
      render: (text) => (
        <Space>
          <PhoneOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '登录邮箱',
      dataIndex: 'login_email',
      key: 'login_email',
      render: (text) => (
        <Space>
          <MailOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag 
          icon={active ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
          color={active ? 'success' : 'default'}
        >
          {active ? '活跃' : '停用'}
        </Tag>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个账号吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理添加账号
  const handleAdd = () => {
    setEditingAccount(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑账号
  const handleEdit = (account) => {
    setEditingAccount(account);
    form.setFieldsValue(account);
    setModalVisible(true);
  };

  // 处理删除账号
  const handleDelete = (id) => {
    setAccounts(accounts.filter(account => account.id !== id));
    message.success('账号删除成功');
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      if (editingAccount) {
        // 更新账号
        setAccounts(accounts.map(account => 
          account.id === editingAccount.id 
            ? { ...account, ...values }
            : account
        ));
        message.success('账号更新成功');
      } else {
        // 添加账号
        const newAccount = {
          id: Date.now(),
          ...values,
          is_active: true,
          last_login: null,
          created_at: new Date().toLocaleString(),
        };
        setAccounts([...accounts, newAccount]);
        message.success('账号添加成功');
      }
      setModalVisible(false);
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // 统计数据
  const stats = {
    total: accounts.length,
    active: accounts.filter(account => account.is_active).length,
    inactive: accounts.filter(account => !account.is_active).length,
  };

  return (
    <div className="accounts-page">
      <div className="page-header">
        <Title level={2}>账号管理</Title>
        <p>管理您的小红书账号，配置登录信息和状态</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总账号数"
              value={stats.total}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="活跃账号"
              value={stats.active}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="停用账号"
              value={stats.inactive}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#8c8c8c' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 账号列表 */}
      <Card
        title="账号列表"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加账号
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={accounts}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 添加/编辑账号模态框 */}
      <Modal
        title={editingAccount ? '编辑账号' : '添加账号'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="account_name"
            label="账号名称"
            rules={[
              { required: true, message: '请输入账号名称' },
              { max: 50, message: '账号名称不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入账号名称" />
          </Form.Item>

          <Form.Item
            name="account_id"
            label="小红书用户ID"
            rules={[
              { required: true, message: '请输入小红书用户ID' },
            ]}
          >
            <Input placeholder="请输入小红书用户ID" />
          </Form.Item>

          <Form.Item
            name="login_phone"
            label="登录手机号"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
            ]}
          >
            <Input placeholder="请输入登录手机号" />
          </Form.Item>

          <Form.Item
            name="login_email"
            label="登录邮箱"
            rules={[
              { type: 'email', message: '请输入正确的邮箱地址' },
            ]}
          >
            <Input placeholder="请输入登录邮箱" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingAccount ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AccountsPage;

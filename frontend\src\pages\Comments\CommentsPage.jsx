import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Tooltip,
  Avatar,
  Drawer,
  List,
  Badge,
} from 'antd';
import {
  MessageOutlined,
  ReplyOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
  SendOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search, TextArea } = Input;
const { RangePicker } = DatePicker;

const CommentsPage = () => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [selectedComment, setSelectedComment] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [noteFilter, setNoteFilter] = useState('all');
  const [dateRange, setDateRange] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    loadComments();
  }, []);

  const loadComments = () => {
    setLoading(true);
    setTimeout(() => {
      setComments([
        {
          id: 1,
          note_id: 1,
          note_title: '美妆分享 - 夏日护肤心得',
          user_name: '小红书用户123',
          user_avatar: null,
          content: '这个护肤方法真的很有效！请问具体的产品品牌是什么呢？',
          reply_status: 'pending',
          reply_content: null,
          reply_time: null,
          created_at: '2024-01-18 10:30:00',
          sentiment: 'positive',
        },
        {
          id: 2,
          note_id: 1,
          note_title: '美妆分享 - 夏日护肤心得',
          user_name: '时尚达人',
          user_avatar: null,
          content: '感谢分享！想知道敏感肌肤可以用吗？',
          reply_status: 'replied',
          reply_content: '敏感肌肤建议先在耳后测试，没有过敏反应再使用哦～',
          reply_time: '2024-01-18 11:15:00',
          created_at: '2024-01-18 09:45:00',
          sentiment: 'neutral',
        },
        {
          id: 3,
          note_id: 2,
          note_title: '时尚穿搭 - 春季搭配指南',
          user_name: '穿搭小白',
          user_avatar: null,
          content: '这套搭配太好看了！请问上衣在哪里买的？',
          reply_status: 'ignored',
          reply_content: null,
          reply_time: null,
          created_at: '2024-01-17 16:20:00',
          sentiment: 'positive',
        },
        {
          id: 4,
          note_id: 3,
          note_title: '美食探店 - 网红咖啡厅推荐',
          user_name: '美食爱好者',
          user_avatar: null,
          content: '这家店的环境真的很棒，咖啡味道怎么样？',
          reply_status: 'pending',
          reply_content: null,
          reply_time: null,
          created_at: '2024-01-18 11:45:00',
          sentiment: 'neutral',
        },
      ]);
      setLoading(false);
    }, 1000);
  };

  // 过滤数据
  const filteredComments = comments.filter(comment => {
    const matchesSearch = comment.content.toLowerCase().includes(searchText.toLowerCase()) ||
                         comment.user_name.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || comment.reply_status === statusFilter;
    const matchesNote = noteFilter === 'all' || comment.note_id.toString() === noteFilter;
    
    let matchesDate = true;
    if (dateRange.length === 2) {
      const commentDate = dayjs(comment.created_at);
      matchesDate = commentDate.isAfter(dateRange[0]) && commentDate.isBefore(dateRange[1]);
    }
    
    return matchesSearch && matchesStatus && matchesNote && matchesDate;
  });

  // 表格列配置
  const columns = [
    {
      title: '用户信息',
      key: 'user',
      width: 150,
      render: (_, record) => (
        <Space>
          <Avatar 
            size="small" 
            icon={<UserOutlined />}
            src={record.user_avatar}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.user_name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {dayjs(record.created_at).format('MM-DD HH:mm')}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '留言内容',
      key: 'content',
      render: (_, record) => (
        <div>
          <div style={{ marginBottom: 8 }}>
            <Text ellipsis={{ tooltip: record.content }} style={{ maxWidth: 300 }}>
              {record.content}
            </Text>
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            来自: {record.note_title}
          </div>
        </div>
      ),
    },
    {
      title: '情感倾向',
      dataIndex: 'sentiment',
      key: 'sentiment',
      width: 100,
      render: (sentiment) => {
        const config = {
          positive: { color: 'green', text: '正面' },
          neutral: { color: 'blue', text: '中性' },
          negative: { color: 'red', text: '负面' },
        };
        return <Tag color={config[sentiment]?.color}>{config[sentiment]?.text}</Tag>;
      },
    },
    {
      title: '回复状态',
      dataIndex: 'reply_status',
      key: 'reply_status',
      width: 120,
      render: (status, record) => {
        const config = {
          pending: { color: 'orange', icon: <ClockCircleOutlined />, text: '待回复' },
          replied: { color: 'green', icon: <CheckOutlined />, text: '已回复' },
          ignored: { color: 'default', icon: <CloseOutlined />, text: '已忽略' },
        };
        return (
          <div>
            <Tag icon={config[status]?.icon} color={config[status]?.color}>
              {config[status]?.text}
            </Tag>
            {status === 'replied' && record.reply_time && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                {dayjs(record.reply_time).format('MM-DD HH:mm')}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.reply_status === 'pending' && (
            <Button
              type="link"
              icon={<ReplyOutlined />}
              onClick={() => handleReply(record)}
            >
              回复
            </Button>
          )}
          {record.reply_status === 'pending' && (
            <Button
              type="link"
              icon={<CloseOutlined />}
              onClick={() => handleIgnore(record)}
            >
              忽略
            </Button>
          )}
          {record.reply_status === 'replied' && (
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewReply(record)}
            >
              查看回复
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // 处理查看详情
  const handleViewDetail = (comment) => {
    setSelectedComment(comment);
    setDetailDrawerVisible(true);
  };

  // 处理回复
  const handleReply = (comment) => {
    setSelectedComment(comment);
    form.resetFields();
    setReplyModalVisible(true);
  };

  // 处理忽略
  const handleIgnore = (comment) => {
    setComments(comments.map(c => 
      c.id === comment.id 
        ? { ...c, reply_status: 'ignored' }
        : c
    ));
    message.success('留言已忽略');
  };

  // 处理查看回复
  const handleViewReply = (comment) => {
    Modal.info({
      title: '回复内容',
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <strong>原留言:</strong>
            <div style={{ padding: '8px 12px', background: '#f5f5f5', borderRadius: 4, margin: '8px 0' }}>
              {comment.content}
            </div>
          </div>
          <div>
            <strong>回复内容:</strong>
            <div style={{ padding: '8px 12px', background: '#e6f7ff', borderRadius: 4, margin: '8px 0' }}>
              {comment.reply_content}
            </div>
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            回复时间: {comment.reply_time}
          </div>
        </div>
      ),
      width: 500,
    });
  };

  // 处理提交回复
  const handleSubmitReply = async (values) => {
    try {
      setComments(comments.map(c => 
        c.id === selectedComment.id 
          ? { 
              ...c, 
              reply_status: 'replied',
              reply_content: values.reply_content,
              reply_time: new Date().toLocaleString()
            }
          : c
      ));
      message.success('回复发送成功');
      setReplyModalVisible(false);
    } catch (error) {
      message.error('回复发送失败，请重试');
    }
  };

  // 批量操作
  const handleBatchReply = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要批量回复的留言');
      return;
    }
    // 这里可以打开批量回复的模态框
    message.info('批量回复功能开发中...');
  };

  const handleBatchIgnore = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要批量忽略的留言');
      return;
    }
    setComments(comments.map(c => 
      selectedRowKeys.includes(c.id) 
        ? { ...c, reply_status: 'ignored' }
        : c
    ));
    setSelectedRowKeys([]);
    message.success(`已忽略 ${selectedRowKeys.length} 条留言`);
  };

  // 统计数据
  const stats = {
    total: comments.length,
    pending: comments.filter(c => c.reply_status === 'pending').length,
    replied: comments.filter(c => c.reply_status === 'replied').length,
    ignored: comments.filter(c => c.reply_status === 'ignored').length,
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.reply_status !== 'pending',
    }),
  };

  return (
    <div className="comments-page">
      <div className="page-header">
        <Title level={2}>留言管理</Title>
        <p>管理小红书笔记的留言，进行回复和状态跟踪</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总留言数"
              value={stats.total}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待回复"
              value={stats.pending}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已回复"
              value={stats.replied}
              prefix={<CheckOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已忽略"
              value={stats.ignored}
              prefix={<CloseOutlined />}
              valueStyle={{ color: '#8c8c8c' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Search
              placeholder="搜索留言内容或用户名"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: '100%' }}
              placeholder="回复状态"
            >
              <Option value="all">全部状态</Option>
              <Option value="pending">待回复</Option>
              <Option value="replied">已回复</Option>
              <Option value="ignored">已忽略</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              value={noteFilter}
              onChange={setNoteFilter}
              style={{ width: '100%' }}
              placeholder="所属笔记"
            >
              <Option value="all">全部笔记</Option>
              <Option value="1">美妆分享</Option>
              <Option value="2">时尚穿搭</Option>
              <Option value="3">美食探店</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
              placeholder={['开始日期', '结束日期']}
            />
          </Col>
          <Col span={4} style={{ textAlign: 'right' }}>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadComments}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 批量操作 */}
      {selectedRowKeys.length > 0 && (
        <Card style={{ marginBottom: 16 }}>
          <Space>
            <Text>已选择 {selectedRowKeys.length} 条留言</Text>
            <Button
              type="primary"
              icon={<ReplyOutlined />}
              onClick={handleBatchReply}
            >
              批量回复
            </Button>
            <Button
              icon={<CloseOutlined />}
              onClick={handleBatchIgnore}
            >
              批量忽略
            </Button>
          </Space>
        </Card>
      )}

      {/* 留言列表 */}
      <Card title="留言列表">
        <Table
          columns={columns}
          dataSource={filteredComments}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 回复模态框 */}
      <Modal
        title="回复留言"
        open={replyModalVisible}
        onCancel={() => setReplyModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedComment && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <strong>原留言:</strong>
              <div style={{ padding: '8px 12px', background: '#f5f5f5', borderRadius: 4, margin: '8px 0' }}>
                {selectedComment.content}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                来自: {selectedComment.user_name} · {selectedComment.created_at}
              </div>
            </div>
            
            <Form form={form} onFinish={handleSubmitReply}>
              <Form.Item
                name="reply_content"
                label="回复内容"
                rules={[
                  { required: true, message: '请输入回复内容' },
                  { max: 500, message: '回复内容不能超过500个字符' },
                ]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入回复内容..."
                  showCount
                  maxLength={500}
                />
              </Form.Item>
              
              <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => setReplyModalVisible(false)}>
                    取消
                  </Button>
                  <Button type="primary" htmlType="submit" icon={<SendOutlined />}>
                    发送回复
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 详情抽屉 */}
      <Drawer
        title="留言详情"
        placement="right"
        onClose={() => setDetailDrawerVisible(false)}
        open={detailDrawerVisible}
        width={500}
      >
        {selectedComment && (
          <div>
            <div style={{ marginBottom: 24 }}>
              <h4>用户信息</h4>
              <Space>
                <Avatar icon={<UserOutlined />} />
                <div>
                  <div>{selectedComment.user_name}</div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {selectedComment.created_at}
                  </div>
                </div>
              </Space>
            </div>
            
            <div style={{ marginBottom: 24 }}>
              <h4>留言内容</h4>
              <div style={{ padding: '12px', background: '#f5f5f5', borderRadius: 4 }}>
                {selectedComment.content}
              </div>
            </div>
            
            <div style={{ marginBottom: 24 }}>
              <h4>所属笔记</h4>
              <div>{selectedComment.note_title}</div>
            </div>
            
            <div style={{ marginBottom: 24 }}>
              <h4>状态信息</h4>
              <Space direction="vertical">
                <div>回复状态: {
                  selectedComment.reply_status === 'pending' ? '待回复' :
                  selectedComment.reply_status === 'replied' ? '已回复' : '已忽略'
                }</div>
                <div>情感倾向: {
                  selectedComment.sentiment === 'positive' ? '正面' :
                  selectedComment.sentiment === 'negative' ? '负面' : '中性'
                }</div>
              </Space>
            </div>
            
            {selectedComment.reply_status === 'replied' && (
              <div>
                <h4>回复内容</h4>
                <div style={{ padding: '12px', background: '#e6f7ff', borderRadius: 4 }}>
                  {selectedComment.reply_content}
                </div>
                <div style={{ fontSize: '12px', color: '#666', marginTop: 8 }}>
                  回复时间: {selectedComment.reply_time}
                </div>
              </div>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default CommentsPage;

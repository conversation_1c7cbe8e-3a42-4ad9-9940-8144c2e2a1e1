"""
留言管理API路由
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from ...models import get_db, User
from ...services.comment_service import CommentService
from ...api.schemas.comment import (
    CommentResponse,
    CommentUpdate,
    CommentReply,
    CommentBatchReply,
    CommentSearchFilter,
    CommentStatusEnum
)
from ...api.responses import success_response, paginated_response
from ...api.deps import get_current_active_user
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=dict)
def get_comments(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    note_id: int = Query(None, description="笔记ID"),
    status: CommentStatusEnum = Query(None, description="留言状态"),
    author_name: str = Query(None, description="作者名称"),
    keyword: str = Query(None, description="搜索关键词"),
    date_from: datetime = Query(None, description="开始日期"),
    date_to: datetime = Query(None, description="结束日期"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取留言列表"""
    try:
        comment_service = CommentService(db)
        
        # 构建过滤器
        filters = CommentSearchFilter(
            note_id=note_id,
            status=status,
            author_name=author_name,
            keyword=keyword,
            date_from=date_from,
            date_to=date_to
        )
        
        # 计算偏移量
        skip = (page - 1) * size
        
        # 获取留言列表和总数
        comments, total = comment_service.get_user_comments(current_user.id, filters, skip, size)
        
        # 构造响应数据
        comments_data = []
        for comment in comments:
            comment_response = CommentResponse(
                id=comment.id,
                note_id=comment.note_id,
                comment_id=comment.comment_id,
                content=comment.content,
                author_name=comment.author_name,
                author_id=comment.author_id,
                parent_comment_id=comment.parent_comment_id,
                publish_time=comment.publish_time,
                status=comment.status,
                reply_content=comment.reply_content,
                replied_at=comment.replied_at,
                created_at=comment.created_at,
                updated_at=comment.updated_at
            )
            comments_data.append(comment_response.dict())
        
        return paginated_response(
            data=comments_data,
            page=page,
            size=size,
            total=total,
            message="获取留言列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取留言列表时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取留言列表失败"
        )


@router.get("/{comment_id}", response_model=dict)
def get_comment(
    comment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取单个留言详情"""
    try:
        comment_service = CommentService(db)
        comment = comment_service.get_comment_by_id(comment_id, current_user.id)
        
        if not comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="留言不存在"
            )
        
        # 构造响应数据
        comment_response = CommentResponse(
            id=comment.id,
            note_id=comment.note_id,
            comment_id=comment.comment_id,
            content=comment.content,
            author_name=comment.author_name,
            author_id=comment.author_id,
            parent_comment_id=comment.parent_comment_id,
            publish_time=comment.publish_time,
            status=comment.status,
            reply_content=comment.reply_content,
            replied_at=comment.replied_at,
            created_at=comment.created_at,
            updated_at=comment.updated_at
        )
        
        return success_response(
            data=comment_response.dict(),
            message="获取留言详情成功"
        )
        
    except HTTPException as e:
        logger.warning(f"获取留言详情失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取留言详情时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取留言详情失败"
        )


@router.put("/{comment_id}", response_model=dict)
def update_comment(
    comment_id: int,
    comment_data: CommentUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """更新留言信息"""
    try:
        comment_service = CommentService(db)
        comment = comment_service.update_comment(comment_id, current_user.id, comment_data)
        
        # 构造响应数据
        comment_response = CommentResponse(
            id=comment.id,
            note_id=comment.note_id,
            comment_id=comment.comment_id,
            content=comment.content,
            author_name=comment.author_name,
            author_id=comment.author_id,
            parent_comment_id=comment.parent_comment_id,
            publish_time=comment.publish_time,
            status=comment.status,
            reply_content=comment.reply_content,
            replied_at=comment.replied_at,
            created_at=comment.created_at,
            updated_at=comment.updated_at
        )
        
        return success_response(
            data=comment_response.dict(),
            message="留言信息更新成功"
        )
        
    except HTTPException as e:
        logger.warning(f"更新留言失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"更新留言时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="留言更新失败，请稍后重试"
        )


@router.post("/{comment_id}/reply", response_model=dict)
def reply_comment(
    comment_id: int,
    reply_content: str = Query(..., description="回复内容"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """回复留言"""
    try:
        comment_service = CommentService(db)
        reply_data = CommentReply(comment_id=comment_id, reply_content=reply_content)
        success = comment_service.reply_comment(current_user.id, reply_data)
        
        if success:
            return success_response(
                data=None,
                message="留言回复成功"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="留言回复失败"
            )
            
    except HTTPException as e:
        logger.warning(f"回复留言失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"回复留言时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="留言回复失败，请稍后重试"
        )


@router.post("/batch-reply", response_model=dict)
def batch_reply_comments(
    batch_reply_data: CommentBatchReply,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """批量回复留言"""
    try:
        comment_service = CommentService(db)
        result = comment_service.batch_reply_comments(current_user.id, batch_reply_data)
        
        return success_response(
            data=result,
            message="批量回复操作完成"
        )
        
    except HTTPException as e:
        logger.warning(f"批量回复失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"批量回复时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量回复失败，请稍后重试"
        )


@router.post("/batch-ignore", response_model=dict)
def batch_ignore_comments(
    comment_ids: List[int],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """批量忽略留言"""
    try:
        comment_service = CommentService(db)
        result = comment_service.mark_comments_as_ignored(current_user.id, comment_ids)
        
        return success_response(
            data=result,
            message="批量忽略操作完成"
        )
        
    except HTTPException as e:
        logger.warning(f"批量忽略失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"批量忽略时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量忽略失败，请稍后重试"
        )


@router.get("/stats/overview", response_model=dict)
def get_comments_stats(
    note_id: int = Query(None, description="笔记ID"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取留言统计信息"""
    try:
        comment_service = CommentService(db)
        stats = comment_service.get_comment_stats(current_user.id, note_id)
        
        return success_response(
            data=stats,
            message="获取留言统计成功"
        )
        
    except HTTPException as e:
        logger.warning(f"获取留言统计失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取留言统计时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取留言统计失败"
        )


@router.get("/pending", response_model=dict)
def get_pending_comments(
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取待处理的留言"""
    try:
        comment_service = CommentService(db)
        comments = comment_service.get_pending_comments(current_user.id, limit)
        
        # 构造响应数据
        comments_data = []
        for comment in comments:
            comment_response = CommentResponse(
                id=comment.id,
                note_id=comment.note_id,
                comment_id=comment.comment_id,
                content=comment.content,
                author_name=comment.author_name,
                author_id=comment.author_id,
                parent_comment_id=comment.parent_comment_id,
                publish_time=comment.publish_time,
                status=comment.status,
                reply_content=comment.reply_content,
                replied_at=comment.replied_at,
                created_at=comment.created_at,
                updated_at=comment.updated_at
            )
            comments_data.append(comment_response.dict())
        
        return success_response(
            data=comments_data,
            message="获取待处理留言成功"
        )
        
    except Exception as e:
        logger.error(f"获取待处理留言时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取待处理留言失败"
        )

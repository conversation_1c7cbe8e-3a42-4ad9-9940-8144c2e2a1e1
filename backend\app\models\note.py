"""
笔记相关数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .database import Base
import enum


class NoteStatus(enum.Enum):
    """笔记状态枚举"""
    ACTIVE = "active"  # 活跃监控中
    PAUSED = "paused"  # 暂停监控
    COMPLETED = "completed"  # 已完成
    ERROR = "error"  # 错误状态


class Note(Base):
    """笔记表"""
    __tablename__ = "notes"
    
    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("xiaohongshu_accounts.id"), nullable=False)
    note_id = Column(String(100), unique=True, index=True, nullable=False)  # 小红书笔记ID
    note_url = Column(String(500), nullable=False)  # 笔记URL
    title = Column(String(200))  # 笔记标题
    content = Column(Text)  # 笔记内容
    author_name = Column(String(100))  # 作者昵称
    author_id = Column(String(100))  # 作者ID
    publish_time = Column(DateTime(timezone=True))  # 发布时间
    likes_count = Column(Integer, default=0)  # 点赞数
    comments_count = Column(Integer, default=0)  # 评论数
    shares_count = Column(Integer, default=0)  # 分享数
    status = Column(Enum(NoteStatus), default=NoteStatus.ACTIVE)  # 监控状态
    last_crawled = Column(DateTime(timezone=True))  # 最后抓取时间
    crawl_interval = Column(Integer, default=300)  # 抓取间隔（秒）
    auto_reply_enabled = Column(Boolean, default=True)  # 是否启用自动回复
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    account = relationship("XiaohongshuAccount", back_populates="notes")
    comments = relationship("Comment", back_populates="note")
    
    def __repr__(self):
        return f"<Note(id={self.id}, note_id='{self.note_id}', title='{self.title}')>"


class Comment(Base):
    """评论表"""
    __tablename__ = "comments"
    
    id = Column(Integer, primary_key=True, index=True)
    note_id = Column(Integer, ForeignKey("notes.id"), nullable=False)
    comment_id = Column(String(100), unique=True, index=True)  # 小红书评论ID
    parent_comment_id = Column(String(100))  # 父评论ID（用于回复）
    user_name = Column(String(100), nullable=False)  # 评论用户昵称
    user_id = Column(String(100))  # 评论用户ID
    content = Column(Text, nullable=False)  # 评论内容
    publish_time = Column(DateTime(timezone=True))  # 发布时间
    likes_count = Column(Integer, default=0)  # 点赞数
    is_replied = Column(Boolean, default=False)  # 是否已回复
    reply_content = Column(Text)  # 回复内容
    reply_time = Column(DateTime(timezone=True))  # 回复时间
    reply_status = Column(String(20), default="pending")  # 回复状态：pending, success, failed
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    note = relationship("Note", back_populates="comments")
    
    def __repr__(self):
        return f"<Comment(id={self.id}, user_name='{self.user_name}', content='{self.content[:50]}...')>"

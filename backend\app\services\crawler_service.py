"""
爬虫集成服务
集成之前开发的小红书爬虫工具
"""
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from ..models.note import Note, NoteStatus
from ..models.comment import Comment, CommentStatus
from ..models.user import <PERSON><PERSON>shuAccount
from .note_service import NoteService
from .comment_service import CommentService
from ..api.schemas.comment import CommentCreate, CrawlResult
import logging
import asyncio
import json

logger = logging.getLogger(__name__)


class CrawlerService:
    """爬虫集成服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.note_service = NoteService(db)
        self.comment_service = CommentService(db)
    
    async def crawl_notes(self, note_ids: Optional[List[int]] = None, force_crawl: bool = False) -> List[CrawlResult]:
        """抓取笔记留言"""
        try:
            # 获取需要抓取的笔记
            if note_ids:
                notes = self.db.query(Note).filter(Note.id.in_(note_ids)).all()
            else:
                notes = self.note_service.get_notes_for_crawling(limit=10)
            
            if not notes:
                logger.info("没有需要抓取的笔记")
                return []
            
            results = []
            
            for note in notes:
                try:
                    result = await self._crawl_single_note(note, force_crawl)
                    results.append(result)
                except Exception as e:
                    logger.error(f"抓取笔记失败 - ID: {note.id}, 错误: {e}")
                    results.append(CrawlResult(
                        note_id=note.id,
                        success=False,
                        new_comments_count=0,
                        error_message=str(e),
                        crawl_time=datetime.utcnow()
                    ))
            
            return results
            
        except Exception as e:
            logger.error(f"批量抓取笔记时发生错误: {e}")
            return []
    
    async def _crawl_single_note(self, note: Note, force_crawl: bool = False) -> CrawlResult:
        """抓取单个笔记的留言"""
        try:
            # 检查是否需要抓取
            if not force_crawl and note.last_crawled:
                time_since_last_crawl = datetime.utcnow() - note.last_crawled
                if time_since_last_crawl.total_seconds() < note.crawl_interval:
                    return CrawlResult(
                        note_id=note.id,
                        success=True,
                        new_comments_count=0,
                        error_message="未到抓取时间",
                        crawl_time=datetime.utcnow()
                    )
            
            # 获取账号信息
            account = self.db.query(XiaohongshuAccount).filter(
                XiaohongshuAccount.id == note.account_id
            ).first()
            
            if not account or not account.cookies:
                return CrawlResult(
                    note_id=note.id,
                    success=False,
                    new_comments_count=0,
                    error_message="账号不存在或未配置Cookies",
                    crawl_time=datetime.utcnow()
                )
            
            # 调用爬虫工具
            crawler_result = await self._call_crawler_tool(note, account)
            
            if crawler_result['success']:
                # 处理抓取到的数据
                new_comments_count = await self._process_crawled_data(note, crawler_result['data'])
                
                # 更新笔记统计信息
                self.note_service.update_note_stats(note.id, crawler_result['note_stats'])
                
                return CrawlResult(
                    note_id=note.id,
                    success=True,
                    new_comments_count=new_comments_count,
                    crawl_time=datetime.utcnow()
                )
            else:
                return CrawlResult(
                    note_id=note.id,
                    success=False,
                    new_comments_count=0,
                    error_message=crawler_result.get('error', '抓取失败'),
                    crawl_time=datetime.utcnow()
                )
                
        except Exception as e:
            logger.error(f"抓取单个笔记时发生错误 - ID: {note.id}, 错误: {e}")
            return CrawlResult(
                note_id=note.id,
                success=False,
                new_comments_count=0,
                error_message=str(e),
                crawl_time=datetime.utcnow()
            )
    
    async def _call_crawler_tool(self, note: Note, account: XiaohongshuAccount) -> Dict:
        """调用爬虫工具"""
        try:
            # 这里集成之前开发的爬虫工具
            # 由于爬虫工具是独立的模块，这里模拟调用过程
            
            # 模拟爬虫结果（实际应该调用真实的爬虫工具）
            mock_result = {
                'success': True,
                'note_stats': {
                    'likes_count': 100,
                    'comments_count': 50,
                    'shares_count': 20,
                    'author_name': '测试作者',
                    'content': '这是一个测试笔记内容'
                },
                'data': {
                    'comments': [
                        {
                            'comment_id': f'comment_{datetime.utcnow().timestamp()}',
                            'content': '这是一条测试留言',
                            'author_name': '测试用户1',
                            'author_id': 'user123',
                            'publish_time': datetime.utcnow().isoformat(),
                            'parent_comment_id': None
                        }
                    ]
                }
            }
            
            # TODO: 实际集成爬虫工具的代码
            # from ..crawler.xiaohongshu_crawler import XiaohongshuCrawler
            # crawler = XiaohongshuCrawler()
            # result = await crawler.crawl_note_comments(note.note_url, account.cookies)
            
            return mock_result
            
        except Exception as e:
            logger.error(f"调用爬虫工具失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _process_crawled_data(self, note: Note, data: Dict) -> int:
        """处理抓取到的数据"""
        try:
            new_comments_count = 0
            
            if 'comments' in data:
                for comment_data in data['comments']:
                    try:
                        # 创建留言数据
                        comment_create = CommentCreate(
                            note_id=note.id,
                            comment_id=comment_data['comment_id'],
                            content=comment_data['content'],
                            author_name=comment_data['author_name'],
                            author_id=comment_data.get('author_id'),
                            parent_comment_id=comment_data.get('parent_comment_id'),
                            publish_time=datetime.fromisoformat(comment_data['publish_time']) if comment_data.get('publish_time') else None
                        )
                        
                        # 检查留言是否已存在
                        existing_comment = self.db.query(Comment).filter(
                            Comment.comment_id == comment_create.comment_id,
                            Comment.note_id == note.id
                        ).first()
                        
                        if not existing_comment:
                            # 创建新留言
                            self.comment_service.create_comment(comment_create)
                            new_comments_count += 1
                            logger.info(f"新留言已保存: {comment_create.comment_id}")
                        
                    except Exception as e:
                        logger.warning(f"处理留言数据失败: {e}")
                        continue
            
            return new_comments_count
            
        except Exception as e:
            logger.error(f"处理抓取数据时发生错误: {e}")
            return 0
    
    def get_crawl_status(self, user_id: int) -> Dict:
        """获取抓取状态"""
        try:
            # 获取用户的笔记统计
            total_notes = self.db.query(Note).join(XiaohongshuAccount).filter(
                XiaohongshuAccount.user_id == user_id
            ).count()
            
            active_notes = self.db.query(Note).join(XiaohongshuAccount).filter(
                XiaohongshuAccount.user_id == user_id,
                Note.status == NoteStatus.ACTIVE
            ).count()
            
            # 获取最近抓取的笔记
            recent_crawled = self.db.query(Note).join(XiaohongshuAccount).filter(
                XiaohongshuAccount.user_id == user_id,
                Note.last_crawled.isnot(None)
            ).order_by(Note.last_crawled.desc()).limit(5).all()
            
            # 获取待处理留言数量
            pending_comments = self.db.query(Comment).join(Note).join(XiaohongshuAccount).filter(
                XiaohongshuAccount.user_id == user_id,
                Comment.status == CommentStatus.NEW
            ).count()
            
            return {
                'total_notes': total_notes,
                'active_notes': active_notes,
                'pending_comments': pending_comments,
                'recent_crawled': [
                    {
                        'note_id': note.id,
                        'title': note.title,
                        'last_crawled': note.last_crawled.isoformat() if note.last_crawled else None,
                        'comments_count': note.comments_count
                    }
                    for note in recent_crawled
                ]
            }
            
        except Exception as e:
            logger.error(f"获取抓取状态时发生错误: {e}")
            return {
                'total_notes': 0,
                'active_notes': 0,
                'pending_comments': 0,
                'recent_crawled': []
            }
    
    async def start_scheduled_crawling(self):
        """启动定时抓取任务"""
        try:
            logger.info("启动定时抓取任务")
            
            while True:
                try:
                    # 获取需要抓取的笔记
                    notes = self.note_service.get_notes_for_crawling(limit=5)
                    
                    if notes:
                        logger.info(f"开始抓取 {len(notes)} 个笔记")
                        results = await self.crawl_notes([note.id for note in notes])
                        
                        success_count = sum(1 for r in results if r.success)
                        logger.info(f"抓取完成: 成功 {success_count}/{len(results)}")
                    
                    # 等待下次抓取
                    await asyncio.sleep(60)  # 每分钟检查一次
                    
                except Exception as e:
                    logger.error(f"定时抓取任务出错: {e}")
                    await asyncio.sleep(60)
                    
        except Exception as e:
            logger.error(f"定时抓取任务启动失败: {e}")

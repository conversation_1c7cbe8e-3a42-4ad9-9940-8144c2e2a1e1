# 更新日志

本文档记录了项目的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划中
- 前端管理界面开发
- AI智能回复功能
- 实时通知系统
- 数据分析和报表
- 性能优化和缓存

## [1.0.0] - 2024-01-01

### 新增
- 🎉 项目初始版本发布
- ✨ 完整的后端API系统
- 🔐 用户认证和权限管理
- 👥 小红书账号管理功能
- 📝 笔记监控和管理
- 💬 留言抓取和回复管理
- 🤖 爬虫集成服务
- 📊 数据统计和分析
- 🐳 Docker容器化部署
- 📚 完整的项目文档

### 技术特性
- FastAPI现代化Web框架
- SQLAlchemy ORM数据库操作
- PostgreSQL关系型数据库
- Playwright自动化浏览器操作
- JWT无状态认证
- Pydantic数据验证
- APScheduler定时任务调度
- 完整的API文档自动生成

## [0.4.0] - 2023-12-25

### 新增
- 🤖 爬虫集成服务
- ⏰ 定时任务调度系统
- 📊 爬虫状态监控
- 🔍 抓取日志管理
- 🧪 爬虫测试功能

### 改进
- 优化数据库查询性能
- 增强错误处理机制
- 完善日志记录系统

### API更新
- `POST /api/v1/crawler/crawl` - 启动抓取任务
- `GET /api/v1/crawler/status` - 获取抓取状态
- `POST /api/v1/crawler/test` - 测试抓取功能
- `GET /api/v1/crawler/logs` - 获取抓取日志
- `POST /api/v1/crawler/schedule/start` - 启动定时任务
- `POST /api/v1/crawler/schedule/stop` - 停止定时任务
- `GET /api/v1/crawler/schedule/status` - 获取定时任务状态

## [0.3.0] - 2023-12-20

### 新增
- 💬 留言管理系统
- 📝 留言回复功能
- 📊 留言统计分析
- 🔍 留言搜索和过滤
- 📦 批量操作支持

### 改进
- 优化API响应格式
- 增强数据验证
- 完善错误处理

### API更新
- `GET /api/v1/comments` - 获取留言列表
- `GET /api/v1/comments/{id}` - 获取留言详情
- `PUT /api/v1/comments/{id}` - 更新留言信息
- `POST /api/v1/comments/{id}/reply` - 回复留言
- `POST /api/v1/comments/batch-reply` - 批量回复
- `POST /api/v1/comments/batch-ignore` - 批量忽略
- `GET /api/v1/comments/stats/overview` - 留言统计
- `GET /api/v1/comments/pending` - 待处理留言

## [0.2.0] - 2023-12-15

### 新增
- 📝 笔记管理系统
- 🔍 笔记监控配置
- 📊 笔记统计信息
- 🔄 批量操作功能
- 🔍 搜索和过滤功能

### 改进
- 优化数据库模型设计
- 增强API文档
- 完善测试覆盖

### API更新
- `POST /api/v1/notes` - 创建笔记
- `GET /api/v1/notes` - 获取笔记列表
- `GET /api/v1/notes/{id}` - 获取笔记详情
- `PUT /api/v1/notes/{id}` - 更新笔记
- `DELETE /api/v1/notes/{id}` - 删除笔记
- `POST /api/v1/notes/batch` - 批量操作
- `GET /api/v1/notes/{id}/stats` - 笔记统计

## [0.1.0] - 2023-12-10

### 新增
- 🔐 用户认证系统
- 👥 小红书账号管理
- 🏗️ 基础项目架构
- 📚 API文档框架
- 🐳 Docker配置

### 技术实现
- FastAPI应用框架搭建
- SQLAlchemy数据库ORM
- JWT认证机制
- 用户权限管理
- 统一响应格式

### API更新
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/me` - 获取用户信息
- `POST /api/v1/auth/change-password` - 修改密码
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/xiaohongshu/accounts` - 创建账号
- `GET /api/v1/xiaohongshu/accounts` - 获取账号列表
- `PUT /api/v1/xiaohongshu/accounts/{id}` - 更新账号
- `DELETE /api/v1/xiaohongshu/accounts/{id}` - 删除账号

## [0.0.1] - 2023-12-01

### 新增
- 🎯 项目初始化
- 📋 技术选型确定
- 🏗️ 项目结构设计
- 🔍 小红书数据抓取研究
- 📊 数据库设计

### 研究成果
- 小红书接口分析工具
- 登录机制研究
- 反爬虫策略分析
- 自动化爬虫原型
- 抓取策略优化

### 基础工具
- `xiaohongshu_analyzer.py` - 接口分析工具
- `login_analyzer.py` - 登录分析工具
- `anti_crawler_analyzer.py` - 反爬虫分析
- `xiaohongshu_crawler.py` - 爬虫原型
- `crawler_strategy.py` - 抓取策略

---

## 版本说明

### 版本号格式
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 更新类型
- **新增**: 新功能
- **改进**: 对现有功能的改进
- **修复**: 问题修复
- **移除**: 移除的功能
- **安全**: 安全相关的修复
- **废弃**: 即将移除的功能

### 发布周期
- **主版本**: 每年1-2次
- **次版本**: 每月1-2次
- **修订版本**: 根据需要随时发布

### 支持政策
- **当前版本**: 完全支持
- **前一个主版本**: 安全更新支持
- **更早版本**: 不再支持

---

## 贡献指南

如果您想为项目做出贡献，请：

1. 查看 [开发指南](docs/development.md)
2. 遵循 [代码规范](docs/development.md#代码规范)
3. 编写测试用例
4. 更新相关文档
5. 提交Pull Request

感谢所有贡献者的支持！ 🙏

"""
用户相关数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .database import Base
import bcrypt


class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    xiaohongshu_accounts = relationship("<PERSON>hongshuAccount", back_populates="user")
    reply_rules = relationship("ReplyRule", back_populates="user")
    
    def set_password(self, password: str):
        """设置密码"""
        salt = bcrypt.gensalt()
        self.hashed_password = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(
            password.encode('utf-8'), 
            self.hashed_password.encode('utf-8')
        )
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"


class XiaohongshuAccount(Base):
    """小红书账号表"""
    __tablename__ = "xiaohongshu_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    account_name = Column(String(100), nullable=False)  # 账号昵称
    account_id = Column(String(100), unique=True, index=True)  # 小红书账号ID
    login_phone = Column(String(20))  # 登录手机号
    login_email = Column(String(100))  # 登录邮箱
    cookies = Column(Text)  # 保存的cookies
    session_data = Column(Text)  # 会话数据
    is_active = Column(Boolean, default=True)
    last_login = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="xiaohongshu_accounts")
    notes = relationship("Note", back_populates="account")
    
    def __repr__(self):
        return f"<XiaohongshuAccount(id={self.id}, account_name='{self.account_name}')>"
